</thought>好的，已经悉知您完成修改和测试。以下是根据当前最新的代码逻辑，为您生成的 `Condition_Node_Processing.md` 文档内容。该文档客观描述了当前的工作流程、核心组件以及数据流转，省略了不必要的背景和评价。

---

# 条件节点(Conditional_LLM)处理逻辑文档

## 1. 核心思想

`Conditional_LLM` 节点的处理由一个专用的策略类 `ConditionalPolicy` 统一管理。该策略遵循“**分析 -> 判断 -> 行动**”的核心模式，旨在确保只有在信息充分的情况下才进行决策，并在必要时通过一次精确提问来补全信息。

## 2. 处理流程

当工作流遇到 `Conditional_LLM` 节点时，`smart_router` 会将控制权交给 `ConditionalPolicy`，其处理流程如下：

```mermaid
graph TD
    A[ConditionalPolicy.decide] --> B["信息分析 (analyze_...)"];
    B --> C{"检查 missing_info 是否为空?"};
    C -- "否 (信息充足)" --> D["LLM决策 (_make_...)"];
    C -- "是 (信息缺失)" --> E{"是否已提问过?"};
    
    D --> F{"决策成功?"};
    F -- "是" --> G["返回 ROUTE 结果"];
    F -- "否" --> H["返回 FALLBACK 结果"];
    
    E -- "否 (首次缺失)" --> I["生成问题 (_generate_...)"];
    I --> J["返回 ASK_USER 结果 (并标记已提问)"];
    
    E -- "是 (已提问过)" --> H;
```

**流程步骤**:
1.  **信息分析**: 每次进入策略时，都会调用 `analyze_known_and_unknown_information` 对当前状态（包含完整的用户画像和对话历史）进行全量分析，产出自然语言形式的 `known_info` 和 `missing_info` 摘要。
2.  **判断**: 检查 `missing_info` 摘要是否为空。
3.  **行动 (Action)**:
    *   **信息充足**: 直接调用 `_make_conditional_decision` 进行决策。如果决策成功，则路由到下一节点；如果LLM返回 "UNHANDLED" 或决策失败，则触发 Fallback。
    *   **信息缺失**:
        *   **首次缺失**: 调用 `_generate_question_to_user` 生成一个针对性的问题，并中断流程等待用户回答。同时在 `state['routing_context']` 中标记该节点为“已提问”。
        *   **再次缺失**: 如果之前已经提问过一次，但分析后信息依然缺失，则直接触发 Fallback，不再重复提问。

To Jos:
第一次提问需标记已经提问，第二次进分析完，通过这个标记判断是否fallback
逻辑也可讨论，是否问 2 次或者更多次(可能用户没看懂问题让我们解释一下，我们后续会换一种问法）用户都没回答上有missing才fallback

如果missing_info已经为空了，那么_make_conditional_decision还返回的不是数字，是UNHANDLED，那么需要记录错误排查问题

## 3. 核心组件与数据结构

### 3.1 `ConditionalPolicy.decide`
- **位置**: `sop_core.py`
- **职责**: 作为 `Conditional_LLM` 节点的总控制器，编排信息分析、提问和决策的完整流程。

### 3.2 `analyze_known_and_unknown_information`
- **位置**: `sop_core.py`
- **职责**: 分析并提炼关于当前决策点的已知与未知信息。
- **输入**: `SOPState` (含 UserProfile, messages), `node_config`
- **输出**: 一个包含以下字段的字典：
  - `known_info`: (string) 对已知信息的自然语言总结。
  - `missing_info`: (string) 对缺失信息的自然语言总结，如果信息充足则为空字符串。
  - `confidence`: (string) "high|medium|low"
  - `reasoning`: (string) LLM的分析过程。

### 3.3 `_generate_question_to_user`
- **位置**: `sop_core.py`
- **职责**: 当分析出有 `missing_info` 时，生成一个高度针对性的、自然的澄清问题。
- **输入**: `node_config`, `SOPState`, `analysis` (分析结果)
- **输出**: (string) 直接发送给用户的提问文本。

### 3.4 `_make_conditional_decision`
- **位置**: `sop_core.py`
- **职责**: 在信息充足时，根据上下文做出最终的路由选择。
- **关键特性**: 调用一个**低 `temperature` (0.1)** 的 LLM 实例，以确保决策的稳定性和可复现性。
- **输入**: `node_config`, `messages`, `next_nodes`, `SOPState`
- **输出**: (string) 下一个节点的ID 或 "FALLBACK_TRIGGERED"。

### 3.5 状态管理 `state['routing_context']`
该字典用于在会话中追踪节点的中间状态。
- **键**: `f"asked_missing_once::{node_id}"`
- **值**: `True`
- **作用**: 用于实现“最多提问一次”的逻辑。当 `ConditionalPolicy` 发出提问后，`execute_routing_action` 函数会设置此标记。

## 4. Prompt 详解

### 4.1 信息分析 (ANALYZE_KNOWN_UNKNOWN_PROMPT)
- **目标**: 提炼决策所需的已知和未知信息。
- **核心输入变量**:
  - `{condition_title}`: 节点标题
  - `{condition_description}`: 节点描述
  - `{user_profile}`: 用户画像JSON字符串
  - `{chat_log}`: 对话历史
  - `{condition_description}`: 节点描述
  - `{condition_option}`: 节点的可用选项详情
- **输出格式**:
  ```json
  {
      "known_info": "Brief summary of what we know for THIS decision",
      "missing_info": "Brief summary of what we still need for THIS decision, leaving blank if sufficient info exists", 
      "confidence": "high|medium|low",
      "reasoning": "Explain how available data maps to decision requirements and why it's sufficient/insufficient"
  }
  ```
To Jos:
添加了 - **User's latest message**: "{user_message}"
输出无改动

### 4.2 问题生成 (GENERATE_QUESTION_PROMPT)
- **目标**: 根据 `missing_info` 摘要生成一个自然、精确的提问。
- **核心输入变量**:
  - `{condition_title}`: 节点标题
  - `{condition_description}`: 节点描述
  - `{condition_option}`: 节点的可用选项详情
  - `{known_info}`: 分析出的已知信息摘要
  - `{missing_info}`: **(关键)** 分析出的缺失信息摘要
  - `{chat_log}`: 对话历史
  - `{user_profile}`: 用户画像JSON字符串
- **输出格式**: (string) 直接给用户的提问文本。

To Jos:
 添加了  - `{condition_description}`: 节点描述
 输出无改动

### 4.3 条件决策 (CONDITIONAL_DECISION_PROMPT)
- **目标**: 依据已知信息，在多个选项中做出最合理的选择。
- **核心输入变量**:
  - `{node_title}`: 节点标题
  - `{condition_description}`: 节点描述
  - `{condition_option}`: 节点的可用选项详情
  - `{known_info}`: **(关键)** 分析出的已知信息摘要，作为决策的主要依据
  - `{user_message}`: 用户的最新消息
  - `{chat_log}`: 对话历史（作为补充参考）
  - `{user_profile}`: 用户画像（作为补充参考）
- **输出格式**:
  ```json
  {
      "decision": <1_based_index_number_or_"UNHANDLED">,
      "confidence": "high|medium|low",
      "reasoning": "brief explanation"
  }
  ```

To Jos:
  添加了  - `{condition_description}`: 节点描述
  删除了 {missing_info}
  输出无改动