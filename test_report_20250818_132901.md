# SOP Executor 测试报告

**生成时间**: 2025-08-18 13:29:01
**测试版本**: V0.1
**SOP数据文件**: data/2fa-sop-0807.json

## 📊 测试摘要

| 指标 | 数值 |
|------|------|
| 总测试用例数 | 1 |
| 成功用例数 | 1 |
| 失败用例数 | 0 |
| **成功率** | **100.0%** |
| 平均执行步数 | 1.0 |
| 平均执行时间 | 2.17秒 |

## 🔍 失败分析

🎉 所有测试用例都成功通过！

## 📋 详细测试结果

| 用例ID | 结果 | 目标节点 | 执行步数 | 执行时间 | 错误信息 |
|--------|------|----------|----------|----------|----------|
| 01 | ✅ 成功 | 01. Request User to chat in ag... | 1 | 2.17s |  |

## 🧠 结构化路由和决策分析

### 用例 01 执行分析

#### 📋 UserProfile信息

- **可用字段数**: 11
- **字段列表**: resetMfaList, canUserProceedAppeal, userAppealResult, hasActiveBlockCase, canProceed2FAResetByRisk, isSmsEnabled, isEmailEnabled, isOther2FAEnabled, isAiAttack, userLoginStatus, loggedUID
- **样本数据**:
  - `resetMfaList`: [{'id': 29872, 'userId': *************, 'resetFact...
  - `canUserProceedAppeal`: True
  - `userAppealResult`: []

#### 🛤️ 节点执行路径

**步骤 1**: `info_expression_node` - 01. Request User to chat in again as Visitor or login to the account User want to perform 2FA reset


## 💬 用户交互记录

### 用例 01 对话记录

**步骤 1**
🤖 **SOP**: Processing 01. Request User to chat in again as Visitor or login to the account User want to perform 2FA reset...

👤 **用户**: Can you please help to verify my identity I have been uploading so many and I get rejected for passing the verification

---

