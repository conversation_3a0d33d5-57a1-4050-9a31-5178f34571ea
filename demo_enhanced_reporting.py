#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强报告演示脚本
展示结构化路由和决策信息的完整测试报告
"""

import json
import os
from pathlib import Path
import sys
sys.path.append('.')
from test_workflow_v0_1 import SOPTestWorkflow

def create_complex_test_case():
    """创建一个更复杂的测试用例，展示多步骤路由"""
    
    # 创建一个模拟的复杂对话剧本
    complex_script = [
        {
            "turn_id": 0,
            "speaker": "agent",
            "text": "Welcome to Binance Customer Support! How can I help you today?"
        },
        {
            "turn_id": 1,
            "speaker": "user",
            "text": "I need help with 2FA reset. I lost my phone and can't access my account."
        },
        {
            "turn_id": 2,
            "speaker": "agent", 
            "text": "I understand you need help with 2FA reset. Let me check your account status."
        },
        {
            "turn_id": 3,
            "speaker": "user",
            "text": "Yes, I'm logged in but I can't complete transactions because of the 2FA."
        },
        {
            "turn_id": 4,
            "speaker": "agent",
            "text": "Are you requesting to reset 2FA on the account you're currently logged into?"
        },
        {
            "turn_id": 5,
            "speaker": "user",
            "text": "Yes, this is my account and I want to reset the 2FA on this account."
        },
        {
            "turn_id": 6,
            "speaker": "agent",
            "text": "I can see you have SMS and Email enabled. Do you have access to your registered email?"
        },
        {
            "turn_id": 7,
            "speaker": "user",
            "text": "Yes, I can access my email but not my phone for SMS."
        },
        {
            "turn_id": 8,
            "speaker": "agent",
            "text": "Perfect! I can help you reset your 2FA. Let me guide you through the self-reset process."
        },
        {
            "turn_id": 9,
            "speaker": "user",
            "text": "That sounds great, please help me with the steps."
        }
    ]
    
    return {
        "case_id": "complex_demo",
        "source_file": "demo_complex.csv",
        "statistics": {
            "total_turns": len(complex_script),
            "user_turns": len([t for t in complex_script if t["speaker"] == "user"]),
            "agent_turns": len([t for t in complex_script if t["speaker"] == "agent"]),
            "original_messages": len(complex_script)
        },
        "full_script": complex_script
    }

def create_enhanced_userprofile():
    """创建增强的UserProfile，展示更多决策信息"""
    return {
        "resetMfaList": [
            {
                "id": 29872,
                "userId": *************,
                "resetFactor": "SMS",
                "bizType": "RESET_MFA", 
                "status": "AUTO_AUDIT_PASS"
            }
        ],
        "canUserProceedAppeal": True,
        "userAppealResult": [
            {
                "userId": *************,
                "appealName": "previous-appeal",
                "appealFrom": "basicTemplate",
                "status": "APPROVED"
            }
        ],
        "hasActiveBlockCase": False,
        "canProceed2FAResetByRisk": True,
        "isSmsEnabled": True,
        "isEmailEnabled": True,
        "isOther2FAEnabled": True,
        "isAiAttack": False,
        "userLoginStatus": True,
        "loggedUID": *************,
        "accountVerificationLevel": "VERIFIED",
        "riskScore": "LOW",
        "lastLoginTime": "2025-08-18T10:30:00Z",
        "deviceFingerprint": "trusted_device_12345"
    }

def run_enhanced_demo():
    """运行增强的演示，展示完整的结构化报告"""
    
    print("🚀 增强报告演示 - 结构化路由和决策分析")
    print("=" * 70)
    
    # 创建测试工作流
    workflow = SOPTestWorkflow()
    
    # 创建复杂测试用例
    complex_case = create_complex_test_case()
    
    # 临时保存测试用例
    temp_cases_file = "temp_complex_cases.json"
    temp_data = {
        "metadata": {
            "total_cases": 1,
            "processed_at": "2025-08-18T13:30:00",
            "source_directory": "demo"
        },
        "cases": [complex_case]
    }
    
    with open(temp_cases_file, 'w', encoding='utf-8') as f:
        json.dump(temp_data, f, ensure_ascii=False, indent=2)
    
    # 创建临时Ground Truth
    temp_gt_file = "temp_ground_truth.json"
    temp_gt = {
        "complex_demo": {
            "case_file": "demo_complex.csv",
            "ground_truth_node_title": "12. guide user to self-reset 2FA",
            "description": "引导用户自助重置2FA的演示用例"
        }
    }
    
    with open(temp_gt_file, 'w', encoding='utf-8') as f:
        json.dump(temp_gt, f, ensure_ascii=False, indent=2)
    
    try:
        # 加载测试数据
        if not workflow.load_test_data(temp_cases_file, temp_gt_file):
            print("❌ 无法加载测试数据")
            return
        
        print("✅ 已加载复杂测试用例")
        print(f"   剧本长度: {len(complex_case['full_script'])} 个回合")
        print(f"   用户回合: {complex_case['statistics']['user_turns']} 个")
        print(f"   客服回合: {complex_case['statistics']['agent_turns']} 个")
        
        # 修改工作流以使用增强的UserProfile
        original_simulate = workflow.simulate_sop_path
        
        def enhanced_simulate(case_data):
            """增强的模拟函数，使用更丰富的UserProfile"""
            case_id = case_data['case_id']
            script = case_data['full_script']
            
            print(f"\n🎬 开始增强模拟: {case_id}")
            
            # 使用增强的UserProfile
            enhanced_profile = create_enhanced_userprofile()
            
            from sop_core import SimpleSOPConversation
            conversation = SimpleSOPConversation(
                workflow.sop_data_file, 
                user_profile=enhanced_profile
            )
            
            # 开始对话
            initial_response, initial_info = conversation.start_conversation()
            
            print(f"✅ UserProfile已加载: {len(enhanced_profile)} 个字段")
            print(f"🎯 目标节点: 12. guide user to self-reset 2FA")
            
            # 记录执行信息
            execution_log = {
                'case_id': case_id,
                'start_time': __import__('datetime').datetime.now().isoformat(),
                'initial_response': initial_response,
                'path_trace': [],
                'user_interactions': [],
                'routing_decisions': [],
                'internal_notes': [],
                'userprofile_info': {
                    'available_fields': list(enhanced_profile.keys()),
                    'field_count': len(enhanced_profile),
                    'sample_data': {k: str(v)[:50] + "..." if len(str(v)) > 50 else v 
                                  for k, v in list(enhanced_profile.items())[:5]}
                },
                'final_node': None,
                'success': True,  # 模拟成功
                'error_message': None
            }
            
            # 模拟多步骤执行
            for i in range(1, 4):  # 模拟3个步骤
                current_state = conversation.current_state
                if not current_state:
                    break
                
                current_node = current_state.get('current_node', '')
                node_config = current_state.get('sop_data', {}).get('nodes', {}).get(current_node, {})
                node_title = node_config.get('title', current_node)
                node_type = node_config.get('type', '')
                
                # 记录路径步骤
                path_step = {
                    'step': i,
                    'node_id': current_node,
                    'node_title': node_title,
                    'node_type': node_type,
                    'timestamp': __import__('datetime').datetime.now().isoformat()
                }
                
                if node_type == 'condition_node':
                    # 模拟条件节点
                    path_step['conditions'] = [
                        {'description': '用户已登录且已关联账户', 'condition_text': 'user_logged_in_associated'},
                        {'description': '用户已登录但未关联账户', 'condition_text': 'user_logged_in_not_associated'},
                        {'description': '用户未登录', 'condition_text': 'user_not_logged_in'}
                    ]
                    
                    # 模拟路由决策
                    routing_decision = {
                        'step': i,
                        'timestamp': __import__('datetime').datetime.now().isoformat(),
                        'known_info': ['userLoginStatus=True', 'loggedUID=*************', 'accountVerificationLevel=VERIFIED'],
                        'missing_info': [],
                        'llm_decision': '基于UserProfile，用户已登录且已关联，选择条件1',
                        'current_node': current_node
                    }
                    execution_log['routing_decisions'].append(routing_decision)
                    
                    # 模拟内部记录
                    internal_note = {
                        'step': i,
                        'timestamp': __import__('datetime').datetime.now().isoformat(),
                        'content': f"[SystemAutoRouting] 决策说明: {node_title}\n• 决策依据: UserProfile包含userLoginStatus=True和loggedUID\n• 结论: 用户已登录且已关联，跳过身份验证\n• 来源: UserProfile",
                        'parsed_info': {
                            'title': node_title,
                            'reasoning': 'UserProfile包含userLoginStatus=True和loggedUID',
                            'decision': '用户已登录且已关联，跳过身份验证',
                            'source': 'UserProfile'
                        }
                    }
                    execution_log['internal_notes'].append(internal_note)
                
                execution_log['path_trace'].append(path_step)
                
                # 模拟用户交互
                if i < len(script) and script[i-1]['speaker'] == 'user':
                    user_turn = script[i-1]
                    interaction = {
                        'step': i,
                        'turn_id': user_turn['turn_id'],
                        'user_input': user_turn['text'],
                        'sop_question': f"Processing {node_title}..."
                    }
                    execution_log['user_interactions'].append(interaction)
                
                print(f"   步骤 {i}: {node_type} - {node_title}")
            
            execution_log['end_time'] = __import__('datetime').datetime.now().isoformat()
            execution_log['total_steps'] = 3
            execution_log['final_node'] = "guide_user_to_self_reset_2fa"
            
            return execution_log
        
        # 替换模拟函数
        workflow.simulate_sop_path = enhanced_simulate
        
        # 运行测试
        result = workflow.run_single_test("complex_demo")
        workflow.test_results = [result]
        
        # 生成增强报告
        report_file = workflow.generate_report("enhanced_test_report.md")
        
        if report_file:
            print(f"\n📊 增强报告已生成: {report_file}")
            print("\n🔍 报告包含以下增强信息:")
            print("   ✅ UserProfile详细信息和字段分析")
            print("   ✅ 节点执行路径和类型信息")
            print("   ✅ 条件节点的选项和描述")
            print("   ✅ 系统内部决策记录和推理过程")
            print("   ✅ 路由决策的已知/缺失信息分析")
            print("   ✅ LLM决策过程和依据")
            
            # 显示报告预览
            print(f"\n📖 报告预览:")
            try:
                with open(report_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 显示结构化分析部分
                in_analysis = False
                preview_lines = []
                for line in lines:
                    if "## 🧠 结构化路由和决策分析" in line:
                        in_analysis = True
                    elif in_analysis and line.startswith("## "):
                        break
                    elif in_analysis:
                        preview_lines.append(line.rstrip())
                
                if preview_lines:
                    print("".join(preview_lines[:20]))  # 显示前20行
                    if len(preview_lines) > 20:
                        print("   ... (更多内容请查看完整报告)")
                        
            except Exception as e:
                print(f"⚠️ 无法预览报告: {e}")
        
    finally:
        # 清理临时文件
        for temp_file in [temp_cases_file, temp_gt_file]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        print(f"\n✅ 演示完成！")

if __name__ == "__main__":
    run_enhanced_demo()
