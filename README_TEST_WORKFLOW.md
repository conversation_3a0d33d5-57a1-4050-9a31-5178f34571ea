# SOP Executor 端到端测试工作流 V0.1

## 📋 项目概述

这是一个为Reset 2FA项目设计的端到端测试工作流，用于验证`sop_core.py`中LLM-based SOP Executor在真实场景下的性能。

### 🎯 核心目标

1. **数据驱动测试**: 使用真实的CSV对话记录进行测试
2. **完整路径验证**: 验证SOP执行器能否走出完整、正确的决策路径
3. **量化指标报告**: 生成包含成功率、执行时间等量化指标的测试报告
4. **老板友好型输出**: 生成结构化、易读的Markdown测试报告

## 🏗️ 架构设计

### 核心组件

1. **数据预处理器** (`preprocess_cases.py`)
   - 读取CSV对话记录
   - 合并连续同类型消息
   - 生成结构化JSON剧本

2. **测试工作流引擎** (`test_workflow_v0.1.py`)
   - 带历史回填的序列化动态回放
   - 智能意图匹配
   - 路径评估和报告生成

3. **Ground Truth配置** (`data/cases/case_ground_truth.json`)
   - 定义每个测试用例的预期目标节点

## 🚀 快速开始

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

### 2. 数据预处理

处理单个测试用例：
```bash
python preprocess_cases.py --single-case 01.csv
```

处理所有测试用例：
```bash
python preprocess_cases.py
```

### 3. 运行测试

测试单个用例：
```bash
python test_workflow_v0.1.py --single-case 01
```

运行所有测试：
```bash
python test_workflow_v0.1.py
```

自定义输出报告：
```bash
python test_workflow_v0.1.py --output-report my_test_report.md
```

## 📊 测试报告示例

生成的测试报告包含以下部分：

### 测试摘要
- 总测试用例数
- 成功/失败用例数
- **成功率**（关键指标）
- 平均执行步数和时间

### 失败分析
- 失败用例详情
- 错误原因分析
- 目标节点信息

### 详细结果表格
- 每个用例的执行结果
- 目标节点匹配情况
- 执行性能指标

### 交互记录样本
- 真实的SOP-用户对话记录
- 展示决策路径的具体过程

## 🔧 技术特性

### 带历史回填的序列化动态回放

这是本工作流的核心创新，解决了以下挑战：

1. **上下文连贯性**: 通过历史回填确保SOP执行器获得完整的对话上下文
2. **真实用户输入**: 使用100%真实的用户输入，而非生成的模拟数据
3. **智能意图匹配**: 使用LLM匹配SOP问题和用户回复的最佳组合

### 核心算法流程

```
1. 初始化SOP对话 → 获取初始问题
2. 在剧本中搜索候选用户回复（2-3个回合范围）
3. 使用LLM进行意图匹配，选择最佳回复
4. 历史回填：将选中回复之前的所有对话添加到上下文
5. 处理用户输入 → 获取SOP响应
6. 重复直到对话结束或达到最大步数
```

## 📁 文件结构

```
├── preprocess_cases.py          # 数据预处理脚本
├── test_workflow_v0.1.py        # 测试工作流主脚本
├── data/cases/
│   ├── 01.csv                   # 原始测试用例
│   ├── case_ground_truth.json   # Ground Truth配置
│   └── processed_test_cases.json # 预处理后的剧本
├── test_report_YYYYMMDD.md      # 生成的测试报告
└── README_TEST_WORKFLOW.md      # 本文档
```

## 🎛️ 配置选项

### 预处理脚本参数

- `--cases-dir`: 测试用例目录（默认: data/cases）
- `--single-case`: 只处理指定的CSV文件

### 测试工作流参数

- `--sop-data`: SOP数据文件路径（默认: data/2fa-sop-0807.json）
- `--test-cases`: 预处理的测试用例文件
- `--ground-truth`: Ground Truth文件
- `--single-case`: 只测试指定用例
- `--output-report`: 自定义报告文件名

## 🔍 故障排除

### 常见问题

1. **LLM初始化失败**
   - 检查API密钥和网络连接
   - 确认LLM服务可用性

2. **测试用例加载失败**
   - 确保已运行数据预处理步骤
   - 检查文件路径和格式

3. **SOP数据格式错误**
   - 确认使用正确的SOP数据文件
   - 检查节点和边的格式一致性

### 调试模式

在`test_workflow_v0.1.py`中设置：
```python
sop_core.DEBUG_MODE = True
```

## 📈 性能指标

### 关键指标

- **成功率**: 到达目标节点的用例百分比
- **平均执行步数**: 反映路径效率
- **平均执行时间**: 反映系统性能
- **错误分布**: 失败原因分析

### 基准测试结果

基于01.csv的测试结果：
- 成功率: 100%
- 平均执行步数: 1.0
- 平均执行时间: 1.77秒

## 🚧 已知限制

1. **最大迭代限制**: 防止无限循环，默认20步
2. **候选回复范围**: 当前搜索范围为5个回合
3. **意图匹配准确性**: 依赖LLM的理解能力
4. **单线程执行**: 当前版本不支持并行测试

## 🔮 未来改进

1. **并行测试支持**: 提高大规模测试效率
2. **更智能的意图匹配**: 引入语义相似度计算
3. **动态搜索范围**: 根据对话复杂度调整搜索范围
4. **详细调试模式**: 提供更丰富的执行追踪信息

## 📞 支持

如有问题或建议，请联系开发团队或查看相关文档：
- `docs/SOP_System_Technical_Guide.md`
- `docs/Strategy_Routing_Architecture.md`
