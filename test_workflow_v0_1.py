#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP Executor 端到端测试工作流 V0.1
实现带历史回填的序列化动态回放测试

核心功能：
1. 加载预处理的测试用例和Ground Truth
2. 使用真实用户输入进行SOP路径模拟
3. 智能意图匹配和历史回填
4. 生成结构化测试报告
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from sop_core import SimpleSOPConversation, create_llm
from langchain_core.messages import HumanMessage
import sop_core

class SOPTestWorkflow:
    """SOP测试工作流管理器"""
    
    def __init__(self, sop_data_file: str = "data/2fa-sop-0807.json"):
        self.sop_data_file = sop_data_file
        self.llm = None
        self.test_cases = []
        self.ground_truth = {}
        self.test_results = []
        
        # 初始化LLM用于意图匹配
        self.init_llm()
        
        # 关闭调试模式，避免过多输出
        sop_core.DEBUG_MODE = False
        
    def init_llm(self):
        """初始化LLM"""
        try:
            self.llm = create_llm(temperature=0.1)  # 低温度确保决策一致性
            print("✅ LLM初始化成功")
        except Exception as e:
            print(f"❌ LLM初始化失败: {e}")
            self.llm = None
    
    def load_test_data(self, processed_cases_file: str = "data/cases/processed_test_cases.json",
                      ground_truth_file: str = "data/cases/case_ground_truth.json") -> bool:
        """加载测试数据和Ground Truth"""
        try:
            # 加载预处理的测试用例
            with open(processed_cases_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.test_cases = data.get('cases', [])
            print(f"✅ 加载测试用例: {len(self.test_cases)} 个")
            
            # 加载Ground Truth
            with open(ground_truth_file, 'r', encoding='utf-8') as f:
                self.ground_truth = json.load(f)
            print(f"✅ 加载Ground Truth: {len(self.ground_truth)} 个")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载测试数据失败: {e}")
            return False
    
    def match_user_response(self, sop_question: str, candidate_responses: List[Dict], 
                           conversation_context: List[Dict]) -> Optional[Dict]:
        """使用LLM匹配最合适的用户回复"""
        if not self.llm or not candidate_responses:
            return None
            
        # 构建候选回复文本
        candidates_text = []
        for i, response in enumerate(candidate_responses):
            candidates_text.append(f"{i+1}. {response['text']}")
        
        # 构建上下文
        context_text = ""
        if conversation_context:
            recent_context = conversation_context[-3:]  # 最近3轮对话
            for msg in recent_context:
                role = "客服" if msg['role'] == 'assistant' else "用户"
                context_text += f"{role}: {msg['content']}\n"
        
        # 构建匹配提示
        prompt = f"""你是一个对话意图匹配专家。请根据客服的问题，从候选的用户回复中选择最合适的一个。

对话上下文：
{context_text}

客服当前问题：
{sop_question}

候选用户回复：
{chr(10).join(candidates_text)}

请选择最符合对话逻辑和意图的回复编号（1-{len(candidate_responses)}）。
只返回数字，不要其他解释。"""

        try:
            response = self.llm.invoke([HumanMessage(content=prompt)])
            choice_text = response.content.strip()
            
            # 解析选择
            try:
                choice_idx = int(choice_text) - 1
                if 0 <= choice_idx < len(candidate_responses):
                    return candidate_responses[choice_idx]
            except ValueError:
                pass
                
            # 如果解析失败，返回第一个候选
            return candidate_responses[0]
            
        except Exception as e:
            print(f"⚠️ 意图匹配失败: {e}")
            return candidate_responses[0] if candidate_responses else None
    
    def simulate_sop_path(self, case_data: Dict) -> Dict[str, Any]:
        """模拟SOP路径执行 - 核心模拟器逻辑"""
        case_id = case_data['case_id']
        script = case_data['full_script']
        
        print(f"\n🎬 开始模拟案例: {case_id}")
        print(f"   剧本长度: {len(script)} 个回合")
        
        # 创建示例UserProfile用于演示
        sample_userprofile = {
            "resetMfaList": [{
                "id": 29872,
                "userId": 1000262320260,
                "resetFactor": "SMS",
                "bizType": "RESET_MFA",
                "status": "AUTO_AUDIT_PASS"
            }],
            "canUserProceedAppeal": True,
            "userAppealResult": [],
            "hasActiveBlockCase": False,
            "canProceed2FAResetByRisk": True,
            "isSmsEnabled": True,
            "isEmailEnabled": True,
            "isOther2FAEnabled": True,
            "isAiAttack": False,
            "userLoginStatus": True,
            "loggedUID": 1000262320260
        }

        # 初始化SOP对话（带UserProfile）
        conversation = SimpleSOPConversation(self.sop_data_file, user_profile=sample_userprofile)
        
        # 开始对话
        initial_response, initial_info = conversation.start_conversation()
        
        # 记录执行路径
        execution_log = {
            'case_id': case_id,
            'start_time': datetime.now().isoformat(),
            'initial_response': initial_response,
            'path_trace': [],
            'user_interactions': [],
            'routing_decisions': [],  # 新增：路由决策记录
            'internal_notes': [],     # 新增：内部记录
            'userprofile_info': None, # 新增：UserProfile信息
            'final_node': None,
            'success': False,
            'error_message': None
        }
        
        # 模拟变量
        last_used_turn_id = -1
        max_iterations = 20  # 防止无限循环
        iteration = 0
        
        try:
            while iteration < max_iterations:
                iteration += 1
                current_state = conversation.current_state
                
                if not current_state:
                    break
                    
                # 检查是否需要用户输入
                if not current_state.get('need_user_input', False):
                    # 不需要用户输入，可能已经结束
                    break
                
                # 获取当前节点信息
                current_node = current_state.get('current_node', '')
                node_config = current_state.get('sop_data', {}).get('nodes', {}).get(current_node, {})
                node_title = node_config.get('title', current_node)
                node_type = node_config.get('type', '')

                # 记录路径
                path_step = {
                    'step': iteration,
                    'node_id': current_node,
                    'node_title': node_title,
                    'node_type': node_type,
                    'timestamp': datetime.now().isoformat()
                }

                # 添加条件节点的详细信息
                if node_type == 'condition_node':
                    conditions = node_config.get('conditions', [])
                    path_step['conditions'] = [
                        {
                            'description': cond.get('description', ''),
                            'condition_text': cond.get('condition', '')
                        } for cond in conditions
                    ]

                execution_log['path_trace'].append(path_step)
                
                # 获取SOP的当前问题/回复
                messages = current_state.get('messages', [])
                if messages and messages[-1].get('role') == 'assistant':
                    sop_question = messages[-1]['content']
                else:
                    sop_question = initial_response
                
                print(f"   步骤 {iteration}: {node_title}")
                print(f"   🤖 SOP: {sop_question[:100]}...")
                
                # 在剧本中寻找合适的用户回复
                search_start = max(0, last_used_turn_id)
                search_end = min(len(script), last_used_turn_id + 5)  # 向前搜索5个回合
                
                candidates = []
                for i in range(search_start, search_end):
                    if i < len(script) and script[i]['speaker'] == 'user':
                        candidates.append({
                            'turn_id': i,
                            'text': script[i]['text'],
                            'distance': abs(i - last_used_turn_id)
                        })
                
                if not candidates:
                    # 如果没有找到候选，扩大搜索范围
                    for i, turn in enumerate(script):
                        if turn['speaker'] == 'user' and i > last_used_turn_id:
                            candidates.append({
                                'turn_id': i,
                                'text': turn['text'],
                                'distance': i - last_used_turn_id
                            })
                            if len(candidates) >= 3:  # 最多3个候选
                                break
                
                if not candidates:
                    print(f"   ❌ 无法找到合适的用户回复")
                    execution_log['error_message'] = "No suitable user response found"
                    break
                
                # 使用LLM匹配最佳回复
                best_match = self.match_user_response(sop_question, candidates, messages)
                
                if not best_match:
                    print(f"   ❌ 意图匹配失败")
                    execution_log['error_message'] = "Intent matching failed"
                    break
                
                chosen_turn_id = best_match['turn_id']
                user_input = best_match['text']
                
                print(f"   👤 用户 (回合{chosen_turn_id}): {user_input[:100]}...")
                
                # 历史回填：将从last_used_turn_id+1到chosen_turn_id的所有对话添加到历史
                if chosen_turn_id > last_used_turn_id:
                    for fill_id in range(last_used_turn_id + 1, chosen_turn_id + 1):
                        if fill_id < len(script):
                            fill_turn = script[fill_id]
                            role = "user" if fill_turn['speaker'] == 'user' else "assistant"
                            current_state['messages'].append({
                                'role': role,
                                'content': fill_turn['text'],
                                'is_backfill': True,
                                'original_turn_id': fill_id
                            })
                
                # 记录用户交互
                execution_log['user_interactions'].append({
                    'step': iteration,
                    'turn_id': chosen_turn_id,
                    'user_input': user_input,
                    'sop_question': sop_question
                })
                
                # 处理用户输入
                response, step_info = conversation.process_user_input(user_input)

                # 收集内部记录和路由决策
                self._collect_routing_info(conversation.current_state, execution_log, iteration)

                # 更新指针
                last_used_turn_id = chosen_turn_id

                print(f"   🔄 SOP响应: {response[:100]}...")

                # 检查是否结束
                if step_info.get('conversation_ended', False):
                    execution_log['success'] = True
                    break
                    
        except Exception as e:
            print(f"   ❌ 模拟过程出错: {e}")
            execution_log['error_message'] = str(e)
        
        # 记录最终状态
        if conversation.current_state:
            execution_log['final_node'] = conversation.current_state.get('current_node')
            
        execution_log['end_time'] = datetime.now().isoformat()
        execution_log['total_steps'] = iteration
        
        print(f"   ✅ 模拟完成: {iteration} 步")
        
        return execution_log

    def _collect_routing_info(self, current_state: Dict, execution_log: Dict, step: int) -> None:
        """收集路由决策和内部记录信息"""
        from sop_core import INTERNAL_NOTE_TAG

        messages = current_state.get('messages', [])

        # 收集内部记录
        for msg in messages:
            content = msg.get('content', '')
            if (content.startswith(INTERNAL_NOTE_TAG) and
                msg.get('is_internal_record', False)):

                # 解析内部记录
                internal_note = {
                    'step': step,
                    'timestamp': datetime.now().isoformat(),
                    'content': content,
                    'parsed_info': self._parse_internal_note(content)
                }
                execution_log['internal_notes'].append(internal_note)

        # 收集条件上下文信息
        condition_context = current_state.get('condition_context')
        if condition_context:
            routing_decision = {
                'step': step,
                'timestamp': datetime.now().isoformat(),
                'known_info': condition_context.get('known_info', []),
                'missing_info': condition_context.get('missing_info', []),
                'llm_decision': current_state.get('llm_decision'),
                'current_node': current_state.get('current_node', '')
            }
            execution_log['routing_decisions'].append(routing_decision)

        # 收集UserProfile信息（只在第一步收集）
        if step == 1 and not execution_log.get('userprofile_info'):
            user_profile = current_state.get('user_profile')
            if user_profile:
                execution_log['userprofile_info'] = {
                    'available_fields': list(user_profile.keys()),
                    'field_count': len(user_profile),
                    'sample_data': {k: str(v)[:50] + "..." if len(str(v)) > 50 else v
                                  for k, v in list(user_profile.items())[:3]}
                }

    def _parse_internal_note(self, content: str) -> Dict[str, str]:
        """解析内部记录内容"""
        parsed = {}
        lines = content.split('\n')

        for line in lines:
            if '决策依据:' in line:
                parsed['reasoning'] = line.split('决策依据:')[1].strip()
            elif '结论:' in line:
                parsed['decision'] = line.split('结论:')[1].strip()
            elif '来源:' in line:
                parsed['source'] = line.split('来源:')[1].strip()
            elif '决策说明:' in line:
                parsed['title'] = line.split('决策说明:')[1].strip()

        return parsed

    def evaluate_path(self, execution_log: Dict, case_id: str) -> Dict[str, Any]:
        """评估执行路径"""
        ground_truth = self.ground_truth.get(case_id, {})
        target_node_title = ground_truth.get('ground_truth_node_title', '')
        
        # 检查路径中是否包含目标节点
        path_titles = [step['node_title'] for step in execution_log.get('path_trace', [])]
        
        success = any(target_node_title in title for title in path_titles)
        
        return {
            'case_id': case_id,
            'target_node': target_node_title,
            'predicted_path': path_titles,
            'success': success,
            'total_steps': execution_log.get('total_steps', 0),
            'error_message': execution_log.get('error_message'),
            'execution_time': self._calculate_execution_time(execution_log)
        }
    
    def _calculate_execution_time(self, execution_log: Dict) -> float:
        """计算执行时间（秒）"""
        try:
            start_time = datetime.fromisoformat(execution_log['start_time'])
            end_time = datetime.fromisoformat(execution_log['end_time'])
            return (end_time - start_time).total_seconds()
        except:
            return 0.0

    def run_single_test(self, case_id: str) -> Dict[str, Any]:
        """运行单个测试用例"""
        # 查找测试用例
        case_data = None
        for case in self.test_cases:
            if case['case_id'] == case_id:
                case_data = case
                break

        if not case_data:
            return {
                'case_id': case_id,
                'error': f'测试用例 {case_id} 未找到'
            }

        # 执行模拟
        execution_log = self.simulate_sop_path(case_data)

        # 评估结果
        evaluation = self.evaluate_path(execution_log, case_id)

        # 合并结果
        result = {
            **evaluation,
            'execution_log': execution_log
        }

        return result

    def run_all_tests(self) -> List[Dict[str, Any]]:
        """运行所有测试用例"""
        print(f"\n🚀 开始运行 {len(self.test_cases)} 个测试用例")
        print("=" * 60)

        results = []

        for i, case_data in enumerate(self.test_cases, 1):
            case_id = case_data['case_id']
            print(f"\n[{i}/{len(self.test_cases)}] 测试用例: {case_id}")

            result = self.run_single_test(case_id)
            results.append(result)

            # 简要结果
            if result.get('success'):
                print(f"✅ 成功 - 到达目标节点")
            else:
                error = result.get('error_message', '未知错误')
                print(f"❌ 失败 - {error}")

        self.test_results = results
        return results

    def generate_report(self, output_file: Optional[str] = None) -> str:
        """生成测试报告"""
        if not self.test_results:
            return "没有测试结果可生成报告"

        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"test_report_{timestamp}.md"

        # 计算统计指标
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.get('success'))
        failed_tests = total_tests - successful_tests
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0

        avg_steps = sum(r.get('total_steps', 0) for r in self.test_results) / total_tests if total_tests > 0 else 0
        avg_time = sum(r.get('execution_time', 0) for r in self.test_results) / total_tests if total_tests > 0 else 0

        # 生成报告内容
        report_content = f"""# SOP Executor 测试报告

**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**测试版本**: V0.1
**SOP数据文件**: {self.sop_data_file}

## 📊 测试摘要

| 指标 | 数值 |
|------|------|
| 总测试用例数 | {total_tests} |
| 成功用例数 | {successful_tests} |
| 失败用例数 | {failed_tests} |
| **成功率** | **{success_rate:.1f}%** |
| 平均执行步数 | {avg_steps:.1f} |
| 平均执行时间 | {avg_time:.2f}秒 |

## 🔍 失败分析

"""

        # 失败用例分析
        failed_cases = [r for r in self.test_results if not r.get('success')]
        if failed_cases:
            report_content += f"共有 {len(failed_cases)} 个用例失败：\n\n"
            for case in failed_cases:
                case_id = case['case_id']
                error = case.get('error_message', '未知错误')
                target = case.get('target_node', '未知目标')
                report_content += f"- **{case_id}**: {error}\n"
                report_content += f"  - 目标节点: {target}\n"
                report_content += f"  - 执行步数: {case.get('total_steps', 0)}\n\n"
        else:
            report_content += "🎉 所有测试用例都成功通过！\n\n"

        # 详细结果表格
        report_content += """## 📋 详细测试结果

| 用例ID | 结果 | 目标节点 | 执行步数 | 执行时间 | 错误信息 |
|--------|------|----------|----------|----------|----------|
"""

        for result in self.test_results:
            case_id = result['case_id']
            success = "✅ 成功" if result.get('success') else "❌ 失败"
            target = result.get('target_node', '')[:30] + "..." if len(result.get('target_node', '')) > 30 else result.get('target_node', '')
            steps = result.get('total_steps', 0)
            exec_time = f"{result.get('execution_time', 0):.2f}s"
            error_msg = result.get('error_message', '') or ''
            error = error_msg[:20] + "..." if len(error_msg) > 20 else error_msg

            report_content += f"| {case_id} | {success} | {target} | {steps} | {exec_time} | {error} |\n"

        # 结构化路由和决策分析
        report_content += "\n## 🧠 结构化路由和决策分析\n\n"

        # 选择一个成功的用例作为样本
        sample_case = None
        for result in self.test_results:
            if result.get('success') and result.get('execution_log'):
                sample_case = result
                break

        if sample_case:
            case_id = sample_case['case_id']
            execution_log = sample_case['execution_log']

            report_content += f"### 用例 {case_id} 执行分析\n\n"

            # UserProfile信息
            userprofile_info = execution_log.get('userprofile_info')
            if userprofile_info:
                report_content += "#### 📋 UserProfile信息\n\n"
                report_content += f"- **可用字段数**: {userprofile_info['field_count']}\n"
                report_content += f"- **字段列表**: {', '.join(userprofile_info['available_fields'])}\n"
                report_content += "- **样本数据**:\n"
                for key, value in userprofile_info['sample_data'].items():
                    report_content += f"  - `{key}`: {value}\n"
                report_content += "\n"

            # 路径追踪
            path_trace = execution_log.get('path_trace', [])
            if path_trace:
                report_content += "#### 🛤️ 节点执行路径\n\n"
                for step in path_trace:
                    node_title = step['node_title']
                    node_type = step['node_type']
                    step_num = step['step']

                    report_content += f"**步骤 {step_num}**: `{node_type}` - {node_title}\n\n"

                    # 条件节点的详细信息
                    if node_type == 'condition_node' and step.get('conditions'):
                        report_content += "- **可用选项**:\n"
                        for i, condition in enumerate(step['conditions'], 1):
                            desc = condition.get('description', '无描述')
                            report_content += f"  {i}. {desc}\n"
                        report_content += "\n"

            # 内部记录分析
            internal_notes = execution_log.get('internal_notes', [])
            if internal_notes:
                report_content += "#### 🔍 系统内部决策记录\n\n"
                for note in internal_notes:
                    parsed = note.get('parsed_info', {})
                    step_num = note['step']

                    if parsed:
                        report_content += f"**步骤 {step_num} 决策**:\n"
                        if 'title' in parsed:
                            report_content += f"- **节点**: {parsed['title']}\n"
                        if 'reasoning' in parsed:
                            report_content += f"- **决策依据**: {parsed['reasoning']}\n"
                        if 'decision' in parsed:
                            report_content += f"- **结论**: {parsed['decision']}\n"
                        if 'source' in parsed:
                            report_content += f"- **信息来源**: {parsed['source']}\n"
                        report_content += "\n"

            # 路由决策分析
            routing_decisions = execution_log.get('routing_decisions', [])
            if routing_decisions:
                report_content += "#### ⚡ 条件判断和路由决策\n\n"
                for decision in routing_decisions:
                    step_num = decision['step']
                    known_info = decision.get('known_info', [])
                    missing_info = decision.get('missing_info', [])
                    llm_decision = decision.get('llm_decision', '')

                    report_content += f"**步骤 {step_num} 路由分析**:\n"
                    if known_info:
                        report_content += f"- **已知信息** ({len(known_info)}项): {', '.join(known_info[:3])}{'...' if len(known_info) > 3 else ''}\n"
                    if missing_info:
                        report_content += f"- **缺失信息** ({len(missing_info)}项): {', '.join(missing_info[:3])}{'...' if len(missing_info) > 3 else ''}\n"
                    if llm_decision:
                        report_content += f"- **LLM决策**: {llm_decision}\n"
                    report_content += "\n"

        # 交互记录样本
        report_content += "\n## 💬 用户交互记录\n\n"

        if sample_case:
            interactions = sample_case['execution_log'].get('user_interactions', [])[:3]  # 前3轮交互

            if interactions:
                report_content += f"### 用例 {case_id} 对话记录\n\n"

                for interaction in interactions:
                    step = interaction['step']
                    question = interaction['sop_question'][:200] + "..." if len(interaction['sop_question']) > 200 else interaction['sop_question']
                    answer = interaction['user_input'][:200] + "..." if len(interaction['user_input']) > 200 else interaction['user_input']

                    report_content += f"**步骤 {step}**\n"
                    report_content += f"🤖 **SOP**: {question}\n\n"
                    report_content += f"👤 **用户**: {answer}\n\n"
                    report_content += "---\n\n"
            else:
                report_content += "本用例无用户交互记录（可能通过UserProfile完全跳过）。\n\n"
        else:
            report_content += "暂无可用的交互记录样本。\n\n"

        # 保存报告
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📄 测试报告已生成: {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
            return ""

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='SOP Executor 端到端测试工作流 V0.1')
    parser.add_argument('--sop-data', default='data/2fa-sop-0807.json',
                       help='SOP数据文件路径')
    parser.add_argument('--test-cases', default='data/cases/processed_test_cases.json',
                       help='预处理的测试用例文件')
    parser.add_argument('--ground-truth', default='data/cases/case_ground_truth.json',
                       help='Ground Truth文件')
    parser.add_argument('--single-case',
                       help='只测试指定的单个用例 (例如: 01)')
    parser.add_argument('--output-report',
                       help='输出报告文件名')

    args = parser.parse_args()

    # 初始化测试工作流
    workflow = SOPTestWorkflow(args.sop_data)

    # 加载测试数据
    if not workflow.load_test_data(args.test_cases, args.ground_truth):
        print("❌ 测试数据加载失败，退出")
        return

    # 运行测试
    if args.single_case:
        print(f"🎯 运行单个测试用例: {args.single_case}")
        result = workflow.run_single_test(args.single_case)
        workflow.test_results = [result]

        # 打印结果
        if result.get('success'):
            print(f"✅ 测试成功")
        else:
            print(f"❌ 测试失败: {result.get('error_message', '未知错误')}")
    else:
        print("🚀 运行所有测试用例")
        workflow.run_all_tests()

    # 生成报告
    report_file = workflow.generate_report(args.output_report)
    if report_file:
        print(f"\n📊 测试完成！报告文件: {report_file}")

if __name__ == "__main__":
    main()
