#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP (Standard Operating Procedure) 核心实现 - 重构版
基于LangGraph的客户服务工作流引擎

架构特点:
1. 统一节点执行器 - 单一函数处理所有节点类型
2. 集中路由决策 - 智能路由器统一管理状态转换
3. 简化状态管理 - 清晰的状态结构和转换逻辑
4. 高可扩展性 - 易于添加新节点类型和修改处理逻辑
5. LangSmith集成追踪和详细调试信息

主要功能:
- 动态SOP工作流执行
- 多类型节点支持 (Intent, InfoExpression, InfoCollection, Conditional_LLM)
- 智能条件决策和路由
- 可配置的节点处理逻辑
"""

import json
import uuid
import os
import time
from typing import Dict, Any, List, Optional, TypedDict, Literal, Protocol
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
from langgraph.graph import StateGraph, END
from langsmith import traceable

# 导入prompt模板和工具函数
from prompts import (
    ANALYZE_KNOWN_UNKNOWN_PROMPT,
    CONDITIONAL_DECISION_PROMPT,
    GENERATE_QUESTION_PROMPT,
    INFO_COLLECTION_FIRST_INTERACTION_PROMPT,
    INFO_COLLECTION_WITH_USER_INPUT_PROMPT,
    INFO_EXPRESSION_PROMPT,
    INTENT_PROMPT,
    format_prompt
)

# 全局调试/追踪开关
DEBUG_MODE = False
PROMPT_TRACE = True  # 开启后，所有LLM调用都会打印：输入变量、最终prompt、原始返回

# 内部系统路由记录标签（替代旧的 [Engine]）
INTERNAL_NOTE_TAG = "[SystemAutoRouting]"

# 配置LangSmith追踪 - 解耦配置，无需在demo中重复设置
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "SOP Service Agent - V2 Refactored"
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"



# ================================
# 1. 简化的状态定义
# ================================
class SOPState(TypedDict):
    messages: List[Dict[str, str]]
    current_node: str
    sop_data: Dict[str, Any]
    session_id: str
    need_user_input: bool
    llm_decision: Optional[str]
    fallback_info: Optional[Dict[str, Any]]  # 新增：用于存储Fallback的详细信息
    user_profile: Optional[Dict[str, Any]]  # 新增：用于存储用户档案信息
    condition_context: Optional[Dict[str, Any]]  # 新增：条件节点的已知/缺失信息上下文
    routing_context: Optional[Dict[str, Any]]  # 新增：策略路由上下文

# ================================
# 策略化路由架构 - MVP版本
# ================================

class RoutingOutcome(TypedDict):
    """统一路由结果模型"""
    type: Literal["ROUTE", "ASK_USER", "END", "FALLBACK", "STAY"]
    data: Dict[str, Any]  # e.g., {"next_node": "id"} 或 {"question": "...", "options": [...]}

class NodeRoutingPolicy(Protocol):
    """节点路由策略接口"""
    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        """决策路由结果"""
        ...

# ================================
# 2. LLM配置
# ================================
def create_llm(temperature: float = 0.7):
    """创建LLM实例

    Args:
        temperature: 控制输出随机性，0.0-1.0，越低越确定性
                    决策场景建议使用0.1，创意场景使用0.7
    """
    llm = ChatOpenAI(
        model="bedrock-claude-4-sonnet",
        api_key="sk-XVa3N4DCQyUxK1Ulg7PMPw",
        base_url="https://litellm.devfdg.net/v1",
        temperature=temperature,
        max_tokens=200  # type: ignore
    )
    return llm

# ================================
# 2.1 通用工具函数（日志/提取/拼接）
# ================================
def add_internal_note(state: SOPState, title: str, reasoning: str, decision: str, source: str) -> None:
    """
    写入标准化的系统路由记录，统一使用 INTERNAL_NOTE_TAG。
    - title: 当前节点标题
    - reasoning: 决策依据的简述
    - decision: 系统采取的动作/结论
    - source: 信息来源（如 UserProfile、历史对话、系统综合）
    """
    note = (
        f"{INTERNAL_NOTE_TAG} 决策说明: {title}\n"
        f"• 决策依据: {reasoning}\n"
        f"• 结论: {decision}\n"
        f"• 来源: {source}"
    )
    state['messages'].append({
        "role": "assistant",
        "content": note,
        "is_internal_record": True
    })

def conversation_to_text(messages: List[Dict[str, Any]], include_internal: bool = False) -> str:
    """
    将对话历史转为字符串。默认不包含内部路由记录；若包含，会将内部记录前缀转换为 system_internal: 标记。
    """
    parts: List[str] = []
    for msg in messages:
        content = str(msg.get('content', ''))
        if not include_internal and content.startswith(INTERNAL_NOTE_TAG):
            continue
        if include_internal and content.startswith(INTERNAL_NOTE_TAG):
            parts.append(f"system_internal: {content}")
        else:
            parts.append(f"{msg.get('role', 'assistant')}: {content}")
    return "\n".join(parts)

def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    从LLM的原始文本中鲁棒地提取JSON对象。
    - 去除markdown代码块
    - 截取第一个'{'到最后一个'}'之间的子串
    - 失败返回None
    """
    try:
        cleaned = text.strip()

        # 处理markdown代码块
        if cleaned.startswith('```'):
            parts = cleaned.split('```')
            for seg in parts:
                seg = seg.strip()
                if seg.startswith('json'):
                    seg = seg[4:].strip()  # 移除 'json' 前缀
                if seg.startswith('{'):
                    # 尝试找到完整的JSON对象
                    brace_count = 0
                    end_pos = -1
                    for i, char in enumerate(seg):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_pos = i
                                break

                    if end_pos != -1:
                        json_str = seg[:end_pos+1]
                        return json.loads(json_str)

        # 如果markdown处理失败，尝试直接提取
        l = cleaned.find('{')
        r = cleaned.rfind('}')
        if l != -1 and r != -1 and r > l:
            candidate = cleaned[l:r+1]
            return json.loads(candidate)

    except Exception:
        return None
    return None

def invoke_llm_with_trace(llm, prompt_name: str, prompt_text: str, variables: Dict[str, Any]) -> str:
    """
    统一的LLM调用封装：
    - 打印输入变量与最终拼接的prompt
    - 执行调用并打印原始返回
    - 返回文本字符串
    """
    # 注释掉调试打印
    # if PROMPT_TRACE:
    #     try:
    #         print(f"\n===== LLM Prompt: {prompt_name} =====")
    #         print("-- Inputs --")
    #         print(json.dumps(variables, ensure_ascii=False, indent=2))
    #         print("-- Prompt Text --")
    #         print(prompt_text)
    #     except Exception:
    #         pass
    from langchain_core.messages import HumanMessage
    response = llm.invoke([HumanMessage(content=prompt_text)])
    text = str(getattr(response, 'content', response)).strip()
    # if PROMPT_TRACE:
    #     try:
    #         print("-- LLM Raw Output --")
    #         print(text)
    #         print(f"===== End Prompt: {prompt_name} =====\n")
    #     except Exception:
    #         pass
    return text

# ================================
# 3. UserProfile 辅助函数
# ================================

# 全局用户档案字段说明
USER_PROFILE_FIELDS_DESCRIPTION = """**用户档案字段说明：**
| 字段名 | 类型 | 中文说明 |
|--------|------|----------|
| resetMfaList | Array | MFA（二次验证）重置记录列表 |
| canUserProceedAppeal | Boolean | 用户是否可以提交申诉 |
| userAppealResult | Array | 用户的历史申诉记录 |
| hasActiveBlockCase | Boolean | 是否存在进行中的封禁案件 |
| canProceed2FAResetByRisk | Boolean | 是否可由风控系统允许继续进行 2FA 重置 |
| isSmsEnabled | Boolean | 当前账号是否启用了短信验证 |
| isEmailEnabled | Boolean | 当前账号是否启用了邮箱验证 |
| isOther2FAEnabled | Boolean | 当前账号是否启用了其他形式的二次验证（如 Google Authenticator） |
| isAiAttack | Boolean | 是否检测到 AI 风控攻击行为 |
| id | Long | 重置请求唯一 ID |
| userId | Long | 用户 ID |
| resetFactor | String | 重置类型（如 SMS 表示短信验证） |
| bizType | String | 业务类型（此处为 RESET_MFA） |
| status | String | 审核状态（如 AUTO_AUDIT_PASS 表示自动审核通过） |
| appealName | String | 申诉名称（通常为模板名或用户定义） |
| appealFrom | String | 申诉来源（如 basicTemplate 表示来源于默认模板） |"""




def analyze_known_and_unknown_information(state: SOPState, node_config: Dict) -> Dict:
    """
    智能分析节点中哪部分信息已知，哪部分需要询问
    综合分析UserProfile和历史对话，让大模型自动分解语义并映射到已知信息，完全通用，不依赖硬编码业务规则
    注意：此函数只负责信息分析，不做最终决策
    """
    import json

    user_profile = state.get('user_profile')
    if not user_profile:
        return {'analysis_type': 'ask_user', 'known_info': '', 'missing_info': ''}

    # 检查节点类型，跳过start_node和intent_node
    original_type = node_config.get('original_type', '')
    if original_type in ['start_node', 'intent_node']:
        return {'analysis_type': 'ask_user', 'known_info': '', 'missing_info': ''}

    node_title = node_config.get('title', '')
    conditions = node_config.get('conditions', [])

    # 构建条件选项的详细信息（排除handle字段，包含description）
    condition_details = []
    for i, cond in enumerate(conditions):
        option_index = i + 1  # 统一使用从1开始的索引
        condition_detail = {
            "index": option_index,
            "text": cond.get('logicOperator', f'Option {option_index}')
        }
        # 添加condition的description（如果存在且不为空）
        condition_desc = cond.get('description', '').strip()
        if condition_desc:
            condition_detail["description"] = condition_desc
        condition_details.append(condition_detail)

    # 获取历史对话信息
    messages = state.get('messages', [])
    conversation_history = []
    for msg in messages:
        # 不用排除内部记录/引擎记录，这些有助于engine决策
        content = msg.get('content', '')
        conversation_history.append(f"{msg['role']}: {content}")

    conversation_text = "\n".join(conversation_history[-10:])  # 只取最近10条消息

    # 准备prompt变量
    node_description_text = node_config.get('description', 'No description available')

    # 预先计算 condition_details_json，避免在prompt中使用表达式
    condition_list = []
    for c in condition_details:
        condition_item = {
            "text": c["text"],
            "description": c.get("description", "")
        }
        condition_list.append(condition_item)
    condition_details_json = json.dumps(condition_list, ensure_ascii=False)

    # 预先计算 user_profile_json
    user_profile_json = json.dumps(user_profile, ensure_ascii=False)

    # 准备conversation摘要
    conversation_summary = conversation_text if conversation_text else "No conversation history"

    # 获取用户最新消息
    messages = state.get('messages', [])
    user_message = ""
    if messages:
        # 获取最后一条用户消息
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break

    # 使用统一的prompt模板
    prompt_variables = {
        'condition_title': node_title,
        'condition_description': node_description_text,
        'user_profile': user_profile_json,
        'chat_log': conversation_summary,
        'condition_option': condition_details_json,
        'user_message': user_message,
        'user_profile_fields_description': USER_PROFILE_FIELDS_DESCRIPTION
    }

    prompt = format_prompt(ANALYZE_KNOWN_UNKNOWN_PROMPT, **prompt_variables)

    try:
        llm = create_llm()
        # 使用prompt_variables作为追踪变量
        response_text = invoke_llm_with_trace(llm, "Semantic.KnownMissingAnalysis", prompt, prompt_variables)

        # 移除过多的调试输出

        # 提取JSON部分
        result = extract_json_from_text(response_text) or {}

        # 调试JSON解析
        if DEBUG_MODE and not result:
            print(f"   ⚠️ JSON解析失败: {response_text[:100]}...")
            # 尝试手动解析
            try:
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    manual_result = json.loads(json_match.group())
                    result = manual_result
                    print(f"   ✅ 手动解析成功")
            except Exception:
                print(f"   ❌ 手动解析失败")

        # 直接使用简化的分析结果
        known_info_summary = result.get('known_info', '')
        missing_info_summary = result.get('missing_info', '')

        if DEBUG_MODE:
            print(f"🔍 [sop_core] 语义分析: {node_title[:60]}...")
            print(f"   已知: {known_info_summary}" if known_info_summary else "   已知: 无")
            print(f"   缺失: {missing_info_summary}" if missing_info_summary else "   缺失: 无")
            print(f"   置信度: {result.get('confidence', 'None')}")

        # 直接返回简化的结果
        return {
            'analysis_type': result.get('analysis_type', 'ask_user'),
            'known_info': known_info_summary,
            'missing_info': missing_info_summary,
            'confidence': result.get('confidence', 'low'),
            'reasoning': result.get('reasoning', ''),
            'semantic_analysis': result
        }

    except Exception as e:
        if DEBUG_MODE:
            print(f"❌ 语义分析失败: {e}")
        return {'analysis_type': 'ask_user', 'known_info': '', 'missing_info': ''}



def _build_ordered_next_nodes(node_id: str, node_data: Dict, valid_edges: List[Dict]) -> tuple[List[str], List[Dict]]:
    """为condition节点构建与条件顺序一致的next_nodes，同时过滤无效条件"""
    conditions = node_data.get('conditions', [])
    ordered_next_nodes = []
    valid_conditions = []

    # 为每个条件找到对应的目标节点（排除handle字段）
    for condition in conditions:
        condition_handle = condition.get('handle', '')
        target_node = None

        # 在有效边中查找匹配的边
        for edge in valid_edges:
            if (edge.get('source') == node_id and
                edge.get('sourceHandle') == condition_handle):
                target_node = edge.get('target')
                break

        if target_node:
            # 只有找到有效目标节点的条件才保留
            ordered_next_nodes.append(target_node)
            valid_conditions.append(condition)
        else:
            # 打印被过滤掉的无效条件
            condition_name = condition.get('logicOperator', 'Unknown')
            if DEBUG_MODE:
                print(f"⚠️ 过滤无效条件: {condition_name} (找不到对应的目标节点)")

    return ordered_next_nodes, valid_conditions

# ================================
# 3. SOP数据加载
# ================================
def load_sop_data(file_path: str = "data/new_2fa_sop.json") -> Dict[str, Any]:
    """加载SOP数据，自动适配新旧格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        # 检查数据格式并转换
        if isinstance(raw_data.get('nodes'), list):
            # 新格式：nodes是数组，需要转换为字典格式
            nodes_dict = {}
            start_nodes = []

            for node in raw_data['nodes']:
                node_id = node['id']
                node_data = node.get('data', {})

                # 转换节点数据格式
                converted_node = {
                    'type': node_data.get('type', 'unknown'),
                    'title': node_data.get('value', {}).get('title', ''),
                    'content': node_data.get('value', {}).get('content', ''),
                    'conditions': node_data.get('value', {}).get('conditions', [])
                }

                nodes_dict[node_id] = converted_node

                # 识别start节点
                if converted_node['type'] == 'start_node':
                    start_nodes.append(node_id)

            # 过滤有效边并构建邻接图
            existing_node_ids = set(nodes_dict.keys())
            edges = raw_data.get('edges', [])
            valid_edges = []
            invalid_edges_count = 0

            for edge in edges:
                source = edge.get('source')
                target = edge.get('target')

                # 检查源节点和目标节点是否都存在
                if source in existing_node_ids and target in existing_node_ids:
                    valid_edges.append(edge)
                else:
                    invalid_edges_count += 1

            if DEBUG_MODE and invalid_edges_count > 0:
                print(f"⚠️  过滤掉 {invalid_edges_count} 条无效边")

            # 构建邻接图（只使用有效边）
            adjacency_graph = {}
            for edge in valid_edges:
                source = edge.get('source')
                target = edge.get('target')
                if source and target:
                    if source not in adjacency_graph:
                        adjacency_graph[source] = []
                    adjacency_graph[source].append(target)

            # 将邻接关系添加到每个节点的next_nodes字段
            # 对于condition节点，需要确保next_nodes的顺序与conditions的顺序一致
            for node_id, node_data in nodes_dict.items():
                if node_data.get('type') == 'condition_node' and node_data.get('conditions'):
                    # 为condition节点构建有序的next_nodes，同时过滤无效条件
                    ordered_next_nodes, valid_conditions = _build_ordered_next_nodes(node_id, node_data, valid_edges)
                    node_data['next_nodes'] = ordered_next_nodes
                    node_data['conditions'] = valid_conditions  # 更新为过滤后的有效条件
                    
                    if DEBUG_MODE and len(valid_conditions) != len(node_data.get('conditions', [])):
                        original_count = len(node_data.get('conditions', []))
                        print(f"节点 {node_id}: 过滤条件 {original_count} → {len(valid_conditions)}")
                else:
                    node_data['next_nodes'] = adjacency_graph.get(node_id, [])

            converted_data = {
                'nodes': nodes_dict,
                'adjacency_graph': adjacency_graph,
                'start_nodes': start_nodes,
                'total_nodes': len(nodes_dict),
                'total_edges': len(valid_edges),
                'invalid_edges_filtered': invalid_edges_count
            }

            print(f"Loaded SOP data: {len(nodes_dict)} nodes, {len(valid_edges)} edges")
            return converted_data
        else:
            # 旧格式：直接返回
            print(f"Loaded SOP data: {raw_data.get('total_nodes', 0)} nodes, {raw_data.get('total_edges', 0)} edges")
            return raw_data

    except Exception as e:
        print(f"Failed to load SOP data: {e}")
        return {"nodes": {}, "adjacency_graph": {}, "start_nodes": []}

# ================================
# 4. 统一节点执行器 (核心改进)
# ================================
def execute_node_with_auto_routing(state: SOPState, node_id: str) -> SOPState:
    """执行节点并自动处理路由 - 核心引擎API"""
    current_state = state.copy()
    current_node_id = node_id

    # 循环执行，直到不需要继续路由
    max_auto_routes = 10  # 防止无限循环
    route_count = 0

    while route_count < max_auto_routes:
        # 执行当前节点
        node_executor = create_node_executor(current_node_id)
        if not node_executor:
            break

        updated_state = node_executor(current_state)

        # 检查是否需要继续执行
        if not updated_state.get('continue_execution', False):
            return updated_state

        # 继续执行下一个节点
        next_node_id = updated_state.get('next_node_id')
        if not next_node_id:
            break

        current_node_id = next_node_id
        updated_state['continue_execution'] = False
        updated_state.pop('next_node_id', None)
        current_state = updated_state
        route_count += 1

    return current_state

def create_node_executor(node_id: str):
    """创建特定节点的执行器 - 增强追踪功能"""

    def execute_specific_node(state: SOPState) -> SOPState:
        # 获取节点信息用于追踪
        current_node = node_id
        sop_data = state['sop_data']
        node_config = sop_data['nodes'].get(current_node, {})
        node_type = node_config.get('type', 'Unknown')
        title = node_config.get('title', '')
        original_type = node_config.get('original_type', '')

        # 根据节点类型调用专门的追踪函数
        # print(f"DEBUG: Executing node {current_node[:12]}... type={node_type} title={title}")

        # 在节点执行前打印分隔符（仅对非终端节点）
        # 终端节点的信息将在demo脚本中单独显示
        if DEBUG_MODE and not state.get('conversation_ended', False):
            # 检查是否是InfoExpression终端节点，如果是则不在这里显示
            if node_type == 'InfoExpression':
                next_nodes = node_config.get('next_nodes', [])
                is_terminal = not next_nodes
                if not is_terminal:
                    print(f"\n=== 节点: {title} ===")
            else:
                print(f"\n=== 节点: {title} ===")

        if node_type == 'InfoExpression':
            return _execute_info_expression_node_traced(state, current_node, node_config, title)
        elif node_type == 'InfoCollection':
            return _execute_info_collection_node_traced(state, current_node, node_config, title)
        elif node_type == 'Conditional_LLM':
            return _execute_conditional_llm_node_traced(state, current_node, node_config, title)
        else:
            return _execute_generic_node_traced(state, current_node, node_config, title, node_type)

    return execute_specific_node

# ================================
# 5. 专门的节点类型追踪函数
# ================================



@traceable(name="InfoExpression_Node_Execution", run_type="tool")
def _execute_info_expression_node_traced(state: SOPState, node_id: str, node_config: Dict, title: str) -> SOPState:
    """信息表达节点执行 - 专门追踪"""

    # 生成内容
    content = _generate_info_expression_content(node_config, state)

    # 更新状态
    new_state = state.copy()
    new_state['current_node'] = node_id
    new_state['messages'].append({
        "role": "assistant",
        "content": content,
        "node_type": "InfoExpression",
        "node_title": node_config.get('title', ''),
        "node_id": node_id
    })

    # info_expression_node改造：完全不需要用户输入，直接继续流程
    if DEBUG_MODE:
        print(f"📢 InfoExpression节点直接输出: {title}")

    # 检查是否为终端节点
    next_nodes = node_config.get('next_nodes', [])
    is_terminal = not next_nodes

    if is_terminal:
        if DEBUG_MODE:
            print(f"InfoExpression节点是终端节点，对话结束")
        new_state['conversation_ended'] = True
    else:
        # 非终端节点，设置llm_decision指向下一个节点
        if next_nodes:
            next_node = next_nodes[0]  # InfoExpression节点通常只有一个下一个节点
            new_state['llm_decision'] = next_node
            if DEBUG_MODE:
                print(f"InfoExpression节点设置下一个节点: {next_node}")

    # 无论是否为终端节点，都不需要用户输入
    new_state['need_user_input'] = False

    return new_state

@traceable(name="InfoCollection_Node_Execution", run_type="tool")
def _execute_info_collection_node_traced(state: SOPState, node_id: str, node_config: Dict, title: str) -> SOPState:
    """信息收集节点执行 - 专门追踪"""

    # 先检查userProfile是否包含所需信息（统一入口）
    analysis = analyze_known_and_unknown_information(state, node_config)
    profile_result = None
    if analysis.get('analysis_type') == 'complete_skip' and analysis.get('confidence') == 'high':
        profile_result = {
            'sufficient': True,
            'reasoning': analysis.get('reasoning', ''),
            'extracted_info': analysis.get('extracted_info', {})
        }

    if DEBUG_MODE:
        print(f"🔍 InfoCollection节点UserProfile检查:")
        print(f"   节点: {title}")
        print(f"   节点类型: {node_config.get('type', 'Unknown')}")
        print(f"   原始类型: {node_config.get('original_type', 'Unknown')}")
        print(f"   检查结果: {'可跳过' if profile_result else '不可跳过'}")

    new_state = state.copy()
    new_state['current_node'] = node_id

    if profile_result:
        # userProfile包含足够信息，跳过用户询问
        node_type = node_config.get('type', '')
        if DEBUG_MODE and node_type not in ['start_node', 'intent_node']:
            print(f"✅ 跳过信息收集节点: {title}")
            print(f"   从userProfile提取信息: {profile_result.get('extracted_info', {})}")

        # 直接将提取的信息添加到collected_data中
        if 'collected_data' not in new_state:
            new_state['collected_data'] = {}

        extracted_info = profile_result.get('extracted_info', {})
        new_state['collected_data'].update(extracted_info)

        # 为跳过的节点添加Engine到chat history
        # 判断跳过的信息来源
        reasoning = profile_result.get('reasoning', '')

        # 判断是基于UserProfile还是历史对话跳过
        reasoning_lower = reasoning.lower()
        if any(keyword in reasoning_lower for keyword in [
            'user stated', 'user said', 'user explicitly', 'user clearly stated',
            'user mentioned', 'conversation', 'user confirmed', 'user answered',
            'semantically maps', 'no need to ask again', 'user has already'
        ]):
            source_type = "历史对话分析"
        elif any(keyword in reasoning_lower for keyword in [
            'profile shows', 'user profile', 'profile data', 'profile contains',
            'resetmfalist', 'ismsenabled', 'isemailenabled', 'isother2faenabled'
        ]):
            source_type = "UserProfile数据"
        else:
            source_type = "系统综合判断"

        add_internal_note(
            state=new_state,
            title=title,
            reasoning=reasoning,
            decision=f"已收集到 {title} 相关信息，跳过询问",
            source=source_type
        )
        new_state['need_user_input'] = False  # 不需要用户输入
    else:
        # userProfile信息不足，按原流程询问用户
        content = _generate_info_collection_content(node_config, state)
        new_state['messages'].append({
            "role": "assistant",
            "content": content,
            "node_type": "InfoCollection",
            "node_title": node_config.get('title', ''),
            "node_id": node_id
        })
        new_state['need_user_input'] = True

    return new_state

@traceable(name="Conditional_LLM_Node_Execution", run_type="tool")
def _execute_conditional_llm_node_traced(state: SOPState, node_id: str, node_config: Dict, title: str) -> SOPState:
    """
    条件LLM节点执行 - V3简化版
    此节点本身不再执行任何复杂逻辑（如提问或决策）。
    所有逻辑都已移至 smart_router 及其调用的 ConditionalPolicy 中。
    它的作用是在图中作为一个状态点，触发后续的路由决策。
    """
    if DEBUG_MODE:
        print(f"🔍 [sop_core] Conditional_LLM node: {title[:40]}... - delegating to policy")

    new_state = state.copy()
    new_state['current_node'] = node_id

    # 不再需要用户输入，因为路由策略会决定是否需要
    new_state['need_user_input'] = False

    # 允许执行流继续到smart_router进行决策
    new_state['continue_execution'] = True

    return new_state

@traceable(name="Generic_Node_Execution", run_type="tool")
def _execute_generic_node_traced(state: SOPState, node_id: str, node_config: Dict, title: str, node_type: str) -> SOPState:
    """通用节点执行 - 专门追踪"""

    # 生成内容
    content = _generate_node_content(node_config, state)

    # 更新状态
    new_state = state.copy()
    new_state['current_node'] = node_id
    new_state['messages'].append({"role": "assistant", "content": content})
    new_state['need_user_input'] = True

    return new_state

def _generate_node_content(node_config: Dict, state: SOPState) -> str:
    """
    生成节点内容 - 可扩展的内容生成逻辑
    根据真实数据情况，可以轻松修改每种节点类型的处理逻辑
    """
    node_type = node_config.get('type', 'Unknown')
    title = node_config.get('title', '')
    original_type = node_config.get('original_type', '')
    
    # 起始节点特殊处理
    if original_type == 'start_node':
        return "Welcome! Let's start with your 2FA reset request. How can I help you today?"
    
    # 根据节点类型生成内容 - 易于根据真实数据调整
    content_generators = {
        'Intent': _generate_intent_content,
        'InfoExpression': _generate_info_expression_content,
        'InfoCollection': _generate_info_collection_content
    }
    
    generator = content_generators.get(node_type, _generate_default_content)
    return generator(node_config, state)

def _generate_intent_content(node_config: Dict, state: SOPState) -> str:
    """生成意图节点内容"""
    _ = state  # 未使用但保持接口一致性
    title = node_config.get('title', '')
    description = node_config.get('description', '')

    # 构建节点信息
    node_info = f"Title: {title}"
    if description:
        node_info += f"\nDescription: {description}"

    # 准备prompt变量
    prompt_variables = {
        'node_info': node_info
    }

    try:
        llm = create_llm()
        prompt = format_prompt(INTENT_PROMPT, **prompt_variables)
        return invoke_llm_with_trace(llm, "Intent.Generate", prompt, prompt_variables)
    except Exception as e:
        print(f"Intent content generation failed: {e}")
        return f"{title}\nPlease describe your 2FA issue in detail."

def _generate_info_expression_content(node_config: Dict, state: SOPState) -> str:
    """生成信息表达节点内容 - 完全数据驱动版本"""
    title = node_config.get('title', '')
    description = node_config.get('description', '')

    # 从节点配置中提取guidance和sample_answer（修正错别字）
    guidance_content = ""
    sample_answer = ""

    # 首先尝试从节点配置的顶层字段获取
    guidance_content = node_config.get('guidance', '')
    sample_answer = node_config.get('sample_answer', '')

    # 如果顶层没有，再从raw_data中提取
    if not guidance_content or not sample_answer:
        raw_data = node_config.get('raw_data', {})
        if raw_data:
            value = raw_data.get('value', {})

            # 提取guidance
            if not guidance_content:
                general_config = value.get('generalConfig', {})
                guidance_content = general_config.get('guidance', '')

            # 提取sample_answer（修正字段名）
            if not sample_answer:
                sample_answer = value.get('simpleAnswer', '')  # 保持兼容性，但变量名修正

    # 清理HTML标签
    import re
    if guidance_content:
        guidance_content = re.sub(r'<[^>]+>', '', guidance_content).strip()
    if sample_answer:
        sample_answer = re.sub(r'<[^>]+>', '', sample_answer).strip()

    # 使用LLM生成动态内容，title作为指导但不显示给用户
    llm = create_llm()
    # 预先计算对话串
    conversation = "\n".join([f"{msg['role']}: {msg['content']}" for msg in state['messages']])

    # 构建更详细的prompt，包含description和guidance
    node_info = f"Title: {title}"
    if description:
        node_info += f"\nDescription: {description}"
    if guidance_content:
        node_info += f"\nGuidance: {guidance_content}"

    # 准备prompt变量
    prompt_variables = {
        'node_info': node_info,
        'conversation': conversation
    }

    # 根据是否有sample_answer动态构建sample_answer_section
    if sample_answer:
        prompt_variables['sample_answer_section'] = f"Sample answer (for reference only):\n{sample_answer}"
    else:
        prompt_variables['sample_answer_section'] = ""

    # 使用统一的prompt模板
    prompt = format_prompt(INFO_EXPRESSION_PROMPT, **prompt_variables)

    try:
        # 使用prompt_variables作为追踪变量
        generated_content = invoke_llm_with_trace(llm, "InfoExpression.Generate", prompt, prompt_variables)
        return generated_content
    except Exception as e:
        if DEBUG_MODE:
            print(f"LLM generation failed for info_expression: {e}")
        # 数据驱动的通用fallback - 基于节点信息而非硬编码
        if description:
            return f"I understand you need assistance with: {description}. Let me help you with the next steps."
        elif guidance_content:
            return f"Based on the guidance provided: {guidance_content[:100]}..."
        else:
            return f"I'm here to help you with: {title}. Let me assist you with the next steps."

def _generate_info_collection_content(node_config: Dict, state: SOPState) -> str:
    """生成信息收集节点内容"""
    title = node_config.get('title', '')
    description = node_config.get('description', '')
    collection_info = node_config.get('collection_info', [])

    # 提取guidance字段
    guidance_content = node_config.get('guidance', '')

    # 清理HTML标签
    import re
    if guidance_content:
        guidance_content = re.sub(r'<[^>]+>', '', guidance_content).strip()

    # 使用LLM生成内容，title作为指导
    llm = create_llm()
    conversation = "\n".join([f"{msg['role']}: {msg['content']}" for msg in state['messages']])

    info_list = []
    if collection_info:
        for info in collection_info[:3]:
            info_desc = info.get('infoDescription', '')
            if info_desc:
                info_list.append(info_desc)

    # 检查用户是否已经表达了需求
    user_messages = [msg['content'] for msg in state['messages'] if msg['role'] == 'user']
    has_user_input = len(user_messages) > 0

    # 构建更详细的节点信息
    node_info = f"Title: {title}"
    if description:
        node_info += f"\nDescription: {description}"
    if guidance_content:
        node_info += f"\nGuidance: {guidance_content}"

    # 预计算需要收集的信息字符串
    info_to_collect = ", ".join(info_list) if info_list else "User details"

    # 准备prompt变量
    prompt_variables = {
        'node_info': node_info
    }

    if not has_user_input:
        # 用户还没有说话，先收集基本信息
        prompt = format_prompt(INFO_COLLECTION_FIRST_INTERACTION_PROMPT, **prompt_variables)
    else:
        # 用户已经表达了需求，收集具体信息
        prompt_variables.update({
            'info_to_collect': info_to_collect,
            'conversation': conversation
        })
        prompt = format_prompt(INFO_COLLECTION_WITH_USER_INPUT_PROMPT, **prompt_variables)

    try:
        # 使用prompt_variables作为追踪变量
        generated_content = invoke_llm_with_trace(llm, "InfoCollection.Generate", prompt, prompt_variables)

        if DEBUG_MODE:
            print(f"🔍 InfoCollection LLM生成内容: {generated_content[:100]}...")
            print(f"🔍 节点title: {title}")
            print(f"🔍 Guidance提取: {'有' if guidance_content else '无'}")
            if guidance_content:
                print(f"🔍 Guidance内容: {guidance_content[:100]}...")

        return generated_content
    except Exception as e:
        print(f"LLM generation failed: {e}")
        return "I need to collect some information to help you with your 2FA issue. Please provide the required details."

def _generate_question_to_user(node_config: Dict, state: SOPState, analysis: Dict) -> str:
    """
    统一的问题生成函数 - 根据已知和缺失信息智能生成问题
    替代原来的_generate_targeted_conditional_content和_generate_conditional_content
    """
    title = node_config.get('title', '')
    description = node_config.get('description', '')
    conditions = node_config.get('conditions', [])

    # 现在直接是字符串
    known_info = analysis.get('known_info', '')
    missing_info = analysis.get('missing_info', '')

    # 使用LLM生成智能问题
    llm = create_llm()
    conversation = "\n".join([f"{msg['role']}: {msg['content']}" for msg in state['messages']])

    # 准备节点信息
    node_info = f"Title: {title}"
    if description:
        node_info += f"\nDescription: {description}"

    # 准备条件详情，避免在prompt中使用表达式
    import json
    condition_list = []
    condition_texts = []
    for i, c in enumerate(conditions):
        text_value = c.get("text", c.get("logicOperator", ""))
        description_value = c.get("description", "")
        condition_item = {"text": text_value, "description": description_value}
        condition_list.append(condition_item)

        # 为GENERATE_QUESTION_PROMPT准备条件文本块
        if description_value:
            condition_texts.append(f"- Option {i+1}: \"{text_value}\" (Description: {description_value})")
        else:
            condition_texts.append(f"- Option {i+1}: \"{text_value}\"")

    condition_details_json = json.dumps(condition_list, ensure_ascii=False)
    condition_texts_block = "\n".join(condition_texts)

    # 直接使用字符串格式的信息
    known_info_context = known_info if known_info and known_info.strip() else "None"
    missing_info_context = missing_info if missing_info and missing_info.strip() else "All required information is available"

    # 获取UserProfile数据
    user_profile = state.get('user_profile', {})
    user_profile_json = json.dumps(user_profile, ensure_ascii=False)

    # 准备prompt变量
    prompt_variables = {
        'condition_title': title,
        'condition_description': description or '',
        'condition_option': condition_texts_block,
        'chat_log': conversation,
        'known_info': known_info_context,
        'missing_info': missing_info_context,
        'user_profile': user_profile_json,
        'user_profile_fields_description': USER_PROFILE_FIELDS_DESCRIPTION
    }

    prompt = format_prompt(GENERATE_QUESTION_PROMPT, **prompt_variables)

    try:
        # 使用prompt_variables作为追踪变量
        generated_content = invoke_llm_with_trace(llm, "Conditional.QuestionGeneration", prompt, prompt_variables)

        if DEBUG_MODE:
            print(f"🔍 智能问题生成: {generated_content[:100]}...")

        return generated_content
    except Exception as e:
        print(f"LLM generation failed: {e}")
        # 降级为简单问题
        return f"I need to understand your situation better regarding: {title}. Could you please provide more details?"

def _generate_default_content(node_config: Dict, state: SOPState) -> str:
    """默认内容生成器"""
    _ = state  # 未使用但保持接口一致性
    title = node_config.get('title', '')
    return f"Processing {title}..." if title else "Processing node..."

def _generate_end_message(current_node: str, sop_data: Dict) -> str:
    """
    生成终端节点的结束消息
    统一处理为友好的服务结束消息
    """
    node_config = sop_data['nodes'].get(current_node, {})
    title = node_config.get('title', '')

    # 生成友好的结束消息
    end_messages = [
        "Thank you for using our Binance support service!",
        "If you need further assistance, please don't hesitate to contact us again.",
        "Your account security is important to us. Have a great day!"
    ]

    # 基于title生成自然的结束消息，但不直接显示title
    if "Request User to chat in again" in title or "login to the account" in title:
        return "Thank you for using our Binance support service! For security purposes, please log into your account or start a new chat session as a visitor to proceed with your 2FA reset request. Your account security is important to us!"
    elif "Transfer to Risk Review" in title:
        return "I'll now transfer your case to our Risk Review team for further assistance with your account unlock and 2FA reset. They will be able to help you with the next steps."
    elif "Face Attack disclaimer" in title:
        return "For your security, I need to inform you about potential security concerns. Please confirm if you recognize this 2FA reset request as your own action."
    elif "under review" in title:
        return "Your request is currently under review. Please allow up to 48 hours for processing. We'll contact you once the review is complete."
    else:
        # 通用结束消息
        return "\n".join(end_messages)

# ================================
# 策略实现 - MVP版本
# ================================

class ConditionalPolicy:
    """条件节点策略 - 重构版：信息分析 → 判断 → 行动"""

    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        if DEBUG_MODE:
            print(f"🔍 ConditionalPolicy analyzing node: {node_id}")

        # 1. 每次都重新进行信息分析（确保最新状态）
        analysis = analyze_known_and_unknown_information(state, node_cfg)
        missing_info = analysis.get('missing_info', '')

        if DEBUG_MODE:
            has_missing = bool(missing_info and missing_info.strip())
            print(f"   Missing info: {'Yes' if has_missing else 'No'}")
            if has_missing:
                print(f"   Details: {missing_info[:100]}...")

        # 2. 检查是否有缺失信息
        if missing_info and missing_info.strip():
            # 定义用于追踪的key
            asked_key = f"asked_missing_once::{node_id}"
            has_asked = state.get('routing_context', {}).get(asked_key, False)

            if has_asked:
                # 如果已经提问过一次，但信息仍然缺失，则触发Fallback
                if DEBUG_MODE:
                    print(f"   Already asked once, triggering fallback")
                return {
                    "type": "FALLBACK",
                    "data": {
                        "reason": "MISSING_INFO_UNRESOLVED",
                        "details": f"After asking, the following information is still missing: {missing_info}"
                    }
                }
            else:
                # 首次发现信息缺失，生成问题并要求用户输入
                if DEBUG_MODE:
                    print(f"   First time missing info, asking user")
                question = _generate_question_to_user(node_cfg, state, analysis)
                return {
                    "type": "ASK_USER",
                    "data": {
                        "question": question,
                        "mark_asked": asked_key  # 附带一个标记，要求上层记录已提问
                    }
                }
        else:
            # 3. 信息充足，直接进行决策
            if DEBUG_MODE:
                print(f"   Information sufficient, making decision")

            messages = state.get('messages', [])
            decision = _make_conditional_decision(node_cfg, messages, node_cfg.get('next_nodes', []), state)

            if decision and decision != "FALLBACK_TRIGGERED":
                # 决策成功，路由到下一个节点
                if DEBUG_MODE:
                    print(f"   Decision successful: {decision}")
                return {"type": "ROUTE", "data": {"next_node": decision}}
            else:
                # 决策失败或LLM返回UNHANDLED，触发Fallback
                if DEBUG_MODE:
                    print(f"   Decision failed, triggering fallback")
                return {
                    "type": "FALLBACK",
                    "data": {
                        "reason": "DECISION_FAILED",
                        "details": "Sufficient information was available, but the LLM could not make a confident decision."
                    }
                }

class InfoCollectionPolicy:
    """信息收集节点策略 - 完善版"""

    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        # 先检查UserProfile是否可以跳过
        analysis = analyze_known_and_unknown_information(state, node_cfg)
        if analysis.get('analysis_type') == 'complete_skip' and analysis.get('confidence') == 'high':
            # UserProfile包含足够信息，直接路由
            next_nodes = node_cfg.get('next_nodes', [])
            if next_nodes:
                return {
                    "type": "ROUTE",
                    "data": {"next_node": next_nodes[0]}
                }

        # 检查是否有用户输入
        messages = state.get('messages', [])
        has_user_input = len(messages) > 0 and any(msg['role'] == 'user' for msg in messages)

        if not has_user_input:
            # 生成收集信息的问题
            content = _generate_info_collection_content(node_cfg, state)
            return {
                "type": "ASK_USER",
                "data": {"question": content}
            }
        else:
            # 有用户输入，路由到下一个节点
            next_nodes = node_cfg.get('next_nodes', [])
            if next_nodes:
                return {
                    "type": "ROUTE",
                    "data": {"next_node": next_nodes[0]}
                }
            else:
                return {"type": "END", "data": {}}

class InfoExpressionPolicy:
    """信息表达节点策略 - 完善版"""

    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        # InfoExpression节点需要生成内容并添加到消息中
        content = _generate_info_expression_content(node_cfg, state)

        # 添加助手消息
        state['messages'].append({
            "role": "assistant",
            "content": content,
            "node_type": node_cfg.get('type', ''),
            "node_title": node_cfg.get('title', ''),
            "node_id": node_id
        })

        # 路由到下一个节点
        next_nodes = node_cfg.get('next_nodes', [])
        if next_nodes:
            return {
                "type": "ROUTE",
                "data": {"next_node": next_nodes[0]}
            }
        else:
            return {"type": "END", "data": {}}

class SkipNodePolicy:
    """跳过节点策略 - 用于intent_node和start_node"""

    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        # 跳过节点，直接路由到下一个有效节点
        next_nodes = node_cfg.get('next_nodes', [])
        if next_nodes:
            # 找到第一个存在的节点
            sop_data = state['sop_data']
            for next_node in next_nodes:
                if next_node in sop_data['nodes']:
                    return {
                        "type": "ROUTE",
                        "data": {"next_node": next_node}
                    }
        return {"type": "END", "data": {}}

class DefaultPolicy:
    """默认策略 - 处理未知节点类型"""

    def decide(self, state: SOPState, node_id: str, node_cfg: Dict) -> RoutingOutcome:
        _ = state, node_id  # 标记未使用参数
        next_nodes = node_cfg.get('next_nodes', [])
        if next_nodes:
            return {
                "type": "ROUTE",
                "data": {"next_node": next_nodes[0]}
            }
        else:
            return {"type": "END", "data": {}}

# 策略注册表 - 支持所有节点类型
POLICIES = {
    "Conditional_LLM": ConditionalPolicy(),
    "InfoCollection": InfoCollectionPolicy(),
    "InfoExpression": InfoExpressionPolicy(),
    "intent_node": SkipNodePolicy(),
    "start_node": SkipNodePolicy(),
    "default": DefaultPolicy(),
}

# 特性开关 - 统一路由逻辑
FEATURE_FLAGS = {
    "use_policy_routing": True,
    "policy_whitelist": ["Conditional_LLM", "InfoCollection", "InfoExpression", "intent_node", "start_node"]  # 支持所有节点类型
}

def execute_routing_action(outcome: RoutingOutcome, state: SOPState) -> str:
    """执行路由动作 - 问用户、路由到下一节点、或结束对话"""
    action_type = outcome["type"]
    data = outcome["data"]

    if action_type == "ASK_USER":
        # 问用户：添加问题消息并等待回答
        question = data["question"]

        # 添加助手消息
        state['messages'].append({
            "role": "assistant",
            "content": question,
            "node_type": state['sop_data']['nodes'].get(state['current_node'], {}).get('type', ''),
            "node_title": state['sop_data']['nodes'].get(state['current_node'], {}).get('title', ''),
            "node_id": state['current_node']
        })

        # 标记已询问（避免重复询问）
        if "mark_asked" in data:
            state.setdefault('routing_context', {})[data["mark_asked"]] = True

        state['need_user_input'] = True
        return END

    elif action_type == "ROUTE":
        # 路由到下一节点
        return data["next_node"]

    elif action_type == "FALLBACK":
        # 处理失败情况，记录详细信息
        state['fallback_info'] = {
            "reason": data.get("reason", "unknown"),
            "missing_info": data.get("missing_info", []),
            "timestamp": time.time()
        }
        return END

    elif action_type == "END":
        # 结束对话
        return END

    else:  # STAY
        # 保持当前节点
        return state['current_node']

# ================================
# 5. 智能路由器
# ================================
@traceable(name="Smart_Router_Decision", run_type="tool")
def smart_router(state: SOPState) -> str:
    """
    智能路由器 - 策略化路由版本
    所有路由决策都通过分发到相应的策略来执行。
    """
    current_node = state['current_node']
    sop_data = state['sop_data']
    node_config = sop_data['nodes'].get(current_node, {})
    node_type = node_config.get('original_type') or node_config.get('type', '')

    if DEBUG_MODE:
        print(f"Smart router:")
        print(f"   Current node: {current_node} (type: {node_type})")

    # 检查是否已经有UserProfile决策（优先级最高）
    if state.get('llm_decision'):
        llm_decision = state['llm_decision']
        if DEBUG_MODE:
            print(f"🎯 使用UserProfile决策: {llm_decision}")
        # 清除llm_decision，避免影响后续路由
        state['llm_decision'] = None
        return llm_decision

    # 统一使用策略路由
    try:
        # 根据节点类型选择策略
        policy = POLICIES.get(node_type, POLICIES['default'])
        outcome = policy.decide(state, current_node, node_config)
        return execute_routing_action(outcome, state)
    except Exception as e:
        # 出错时使用默认策略
        if DEBUG_MODE:
            print(f"⚠️ Policy routing failed for node {current_node}, using default policy: {e}")
        outcome = POLICIES['default'].decide(state, current_node, node_config)
        return execute_routing_action(outcome, state)

@traceable(name="Routing_Decision_Logic", run_type="tool")
def _make_routing_decision(current_node: str, messages: List, sop_data: Dict, state: SOPState) -> Optional[str]:
    """
    路由决策逻辑 - 集中化且易于修改，增强追踪
    根据真实数据情况，可以轻松调整决策逻辑

    @deprecated: 此函数已被策略路由系统替代，建议使用 ConditionalPolicy 等策略类
    保留此函数仅为向后兼容，未来版本将移除
    """
    import warnings
    warnings.warn("_make_routing_decision is deprecated, use ConditionalPolicy instead",
                  DeprecationWarning, stacklevel=2)
    node_config = sop_data['nodes'].get(current_node, {})
    node_type = node_config.get('type', '')
    next_nodes = node_config.get('next_nodes', [])

    # ================= 最小有效负载检查 + 超长输入检查 =================
    if len(messages) >= 1 and messages[-1]['role'] == 'user':
        last_user_message = messages[-1]['content']
        user_input = last_user_message.strip() if last_user_message else ""

        # 1. 超长输入前置拦截
        if len(user_input) > 1000:
            print("⚠️ 检测到超长输入，触发 Fallback。")
            return _trigger_fallback(state, node_config, user_input, "INPUT_TOO_LONG")

        # 2. 检查是否完全为空
        if not user_input:
            print("⚠️ 检测到空的用户输入，触发 Fallback。")
            return _trigger_fallback(state, node_config, "", "UNHANDLED")

        # 3. 检查是否只包含无意义的字符
        import re
        if not re.search(r'\w', user_input):
            print(f"⚠️ 检测到只包含非词汇字符的输入 ('{user_input}'), 触发 Fallback。")
            return _trigger_fallback(state, node_config, user_input, "UNHANDLED")

    # 检查是否为终端节点
    if not next_nodes:
        print(f"Reached terminal node: {node_type} - process ended")
        return None

    if DEBUG_MODE:
        print(f"Routing decision: {node_type} node, {len(next_nodes)} next options")

    # 条件节点需要LLM决策
    if node_type == 'Conditional_LLM':
        decision = _make_conditional_decision(node_config, messages, next_nodes, state)
        # 如果是fallback，直接传递给上层处理
        if decision == "FALLBACK_TRIGGERED":
            return decision
        return decision



    # InfoExpression节点：检查是否需要跳过Intent
    elif node_type == 'info_expression_node':
        # 检查下一个节点是否是Intent节点
        if next_nodes:
            next_node_id = next_nodes[0]
            next_node = sop_data['nodes'].get(next_node_id, {})
            next_node_type = next_node.get('type', '')

            if next_node_type == 'intent_node':
                print(f"📢 InfoExpression检测到Intent节点，跳过并路由到后续流程")
                # 跳过Intent，直接路由到Intent的下一个节点
                intent_next_nodes = next_node.get('next_nodes', [])
                if intent_next_nodes:
                    print(f"📢 跳过Intent，直接路由到: {intent_next_nodes[0]}")
                    return intent_next_nodes[0]

        print(f"📢 InfoExpression节点自动路由到: {next_nodes[0]}")
        return next_nodes[0]

    # InfoCollection节点：收集信息后，检查是否收集完成
    elif node_type == 'info_collection_node':
        # 检查是否收集到足够信息（简单逻辑：用户回复了问题）
        if len(messages) >= 2:
            last_user_message = None
            for msg in reversed(messages):
                if msg['role'] == 'user':
                    last_user_message = msg['content']
                    break

            # 移除了之前的 len(last_user_message.strip()) > 5 检查
            # 因为这会阻止 "yes", "no" 等有效的简短回答
            if last_user_message and last_user_message.strip():
                # 选择存在的下一个节点
                for next_node in next_nodes:
                    if next_node in sop_data['nodes']:
                        if DEBUG_MODE:
                            print(f"InfoCollection节点收集到信息，路由到: {next_node}")
                        return next_node
                if DEBUG_MODE:
                    print(f"InfoCollection节点无有效下一节点")
                return None

        # 第一次执行info_collection_node时，不路由，等待用户输入
        print(f"InfoCollection节点等待用户输入")
        return None

    # 其他节点类型的路由逻辑可以在这里扩展

    # 默认路由逻辑：跳过intent_node
    for next_node in next_nodes:
        next_node_config = sop_data['nodes'].get(next_node, {})
        next_node_type = next_node_config.get('type', '')

        # 如果下一个节点是intent_node，跳过它，路由到它的下一个节点
        if next_node_type == 'intent_node':
            intent_next_nodes = next_node_config.get('next_nodes', [])
            if intent_next_nodes:
                print(f"🔧 跳过Intent节点，直接路由到: {intent_next_nodes[0]}")
                return intent_next_nodes[0]

        # 否则正常路由
        print(f"🔧 默认路由到: {next_node}")
        return next_node

    return None

def _make_conditional_decision(node_config: Dict, messages: List, next_nodes: List, state: SOPState) -> Optional[str]:
    """
    条件决策逻辑 - V3: 基于“原则指导”和“结构化输出(含置信度)”的重构方案
    现在包含历史对话上下文以避免重复询问
    """
    # 获取用户最新回答（可能为空，如果是基于已知信息直接决策）
    user_message = next((msg['content'] for msg in reversed(messages) if msg['role'] == 'user'), None) if messages else None

    # 取出条件上下文（known/missing），用于复合判断
    condition_context = state.get('condition_context') or {}
    known_info_ctx = condition_context.get('known_info', [])
    missing_info_ctx = condition_context.get('missing_info', [])

    # 决策逻辑：
    # 1. 如果有缺失信息但没有用户回答，无法决策
    # 2. 如果没有缺失信息，可以基于已知信息和对话历史决策
    # 3. 如果有用户回答，结合已知信息和用户回答决策

    if DEBUG_MODE:
        print(f"🔍 _make_conditional_decision 调试信息:")
        print(f"   missing_info_ctx: {missing_info_ctx}")
        print(f"   known_info_ctx: {known_info_ctx}")
        print(f"   user_message: {user_message}")
        print(f"   messages count: {len(messages) if messages else 0}")

    if missing_info_ctx and not user_message:
        # 有缺失信息但没有用户回答，无法决策
        if DEBUG_MODE:
            print(f"❌ 决策失败: 有缺失信息但没有用户回答")
        return None

    # 如果既没有已知信息也没有用户回答，且没有对话历史，无法决策
    if not known_info_ctx and not user_message and not messages:
        if DEBUG_MODE:
            print(f"❌ 决策失败: 没有任何可用信息")
        return None
    
    title = node_config.get('title', '')
    conditions = node_config.get('conditions', [])
    if not conditions:
        return next_nodes[0] if next_nodes else None
    
    condition_texts = []
    for i, cond in enumerate(conditions):
        option_index = i + 1  # 统一使用从1开始的索引
        logic_op = cond.get('logicOperator', f'Option {option_index}')
        condition_desc = cond.get('description', '').strip()
        if condition_desc:
            condition_texts.append(f"- Option {option_index}: \"{logic_op}\" (Description: {condition_desc})")
        else:
            condition_texts.append(f"- Option {option_index}: \"{logic_op}\"")
    condition_texts_block = "\n".join(condition_texts)
    llm = create_llm(temperature=0.1)  # 使用低temperature以保证决策稳定性

    # 包含历史对话上下文，避免客服重复询问用户已经提到的信息
    # 处理Engine/内部记录，让LLM理解但不暴露给用户
    conversation_parts = []
    for msg in state['messages']:
        if msg['content'].startswith(INTERNAL_NOTE_TAG) or msg['content'].startswith('[内部记录]'):
            # 内部记录：简化显示，强调这是系统内部信息
            conversation_parts.append(f"system_internal: {msg['content']}")
        else:
            conversation_parts.append(f"{msg['role']}: {msg['content']}")

    conversation = "\n".join(conversation_parts)

    # 取出条件上下文（known/missing），用于复合判断
    condition_context = state.get('condition_context') or {}
    known_info_ctx = condition_context.get('known_info', [])
    missing_info_ctx = condition_context.get('missing_info', [])

    # 简化的上下文处理 - 直接处理字符串
    def format_info_for_prompt(info: str) -> str:
        """格式化信息用于prompt"""
        return info.strip() if info and info.strip() else 'None'

    # 移除重复的变量定义，在统一prompt中重新定义

    # 统一prompt设计 - 不区分是否有用户回答，让LLM自己判断信息充分性
    has_user_message = bool(user_message and user_message.strip())

    # 准备已知和缺失信息的摘要 - 现在直接是字符串
    known_info_for_prompt = format_info_for_prompt(known_info_ctx)
    missing_info_for_prompt = format_info_for_prompt(missing_info_ctx)

    # 预计算prompt变量，避免在prompt中使用表达式
    has_user_message_str = "true" if has_user_message else "false"
    user_message_final = user_message if user_message else ""

    # 获取UserProfile数据
    user_profile = state.get('user_profile', {})
    user_profile_json = json.dumps(user_profile, ensure_ascii=False)

    # 准备prompt变量
    prompt_variables = {
        'condition_title': title,
        'condition_description': node_config.get('description', ''),
        'condition_option': condition_texts_block,
        'has_user_message_str': has_user_message_str,
        'user_message': user_message_final,
        'chat_log': conversation,
        'known_info': known_info_for_prompt,
        'missing_info': missing_info_for_prompt,
        'user_profile': user_profile_json,
        'user_profile_fields_description': USER_PROFILE_FIELDS_DESCRIPTION
    }

    decision_prompt = format_prompt(CONDITIONAL_DECISION_PROMPT, **prompt_variables)
    try:
        # 使用prompt_variables作为追踪变量
        response_text = invoke_llm_with_trace(llm, "Conditional.Decision", decision_prompt, prompt_variables)
        try:
            decision_data = extract_json_from_text(response_text) or {}
            decision = decision_data.get('decision')
            confidence = decision_data.get('confidence', 'N/A')
            reasoning = decision_data.get('reasoning', 'No reasoning provided.')

            # 简化调试输出
            decision_type = "用户回答" if has_user_message else "已知信息"
            route_type = f"基于{decision_type}路由到"

            if DEBUG_MODE:
                print(f"✅ [sop_core] LLM决策({decision_type}): {confidence} - {reasoning[:50]}...")

            if isinstance(decision, int):
                # 转换从1开始的索引到从0开始的数组索引
                array_index = decision - 1
                if 0 <= array_index < len(next_nodes):
                    chosen_node = next_nodes[array_index]
                    if chosen_node in state['sop_data']['nodes']:
                        if DEBUG_MODE:
                            print(f"   {route_type}: {chosen_node}")
                        state['llm_decision'] = chosen_node  # 设置为实际的节点ID
                        state['fallback_info'] = None
                        return chosen_node
                    else:
                        print(f"⚠️ Warning: Target node {chosen_node} does not exist in SOP data.")
                else:
                    print(f"⚠️ Decision index out of range: {decision}")
            elif isinstance(decision, str) and decision.upper() == "UNHANDLED":
                print(f"🔄 LLM explicitly decided to fallback (UNHANDLED).")
                return _trigger_fallback(state, node_config, user_message, "UNHANDLED_INTENT")
            else:
                print(f"⚠️ LLM returned an invalid decision value: {decision}")
        except json.JSONDecodeError:
            print(f"⚠️ Failed to parse LLM's JSON response: {response_text}")
        except Exception as e:
            print(f"⚠️ Error parsing LLM decision: {e}")
    except Exception as e:
        print(f"❌ LLM invocation failed: {e}")
        return _trigger_fallback(state, node_config, user_message, "LLM_ERROR")
    print("⚠️ All decision paths failed, triggering fallback.")
    return _trigger_fallback(state, node_config, user_message, "ROUTING_FAILURE")


def _trigger_fallback(state: SOPState, node_config: Dict, user_input: str, reason: str) -> str:
    """
    构建详细的Fallback信息并存入状态
    """
    conditions = node_config.get('conditions', [])
    expected_conditions = []
    for i, cond in enumerate(conditions):
        logic_op = cond.get('logicOperator', f'Option {i+1}')
        condition_desc = cond.get('description', '').strip()
        if condition_desc:
            expected_conditions.append(f"{logic_op} (Description: {condition_desc})")
        else:
            expected_conditions.append(logic_op)

    fallback_details = {
        "reason": reason,  # AMBIGUOUS, IRRELEVANT, LLM_ERROR, UNKNOWN_ERROR
        "current_node_id": state['current_node'],
        "node_question": node_config.get('title', 'N/A'),
        "expected_conditions": expected_conditions,
        "user_input": user_input,
        "timestamp": time.time() if 'time' in globals() else None
    }

    state['fallback_info'] = fallback_details
    state['conversation_ended'] = True  # 标记对话流程结束

    print(f"🚨 Fallback触发: {reason}")
    print(f"   节点问题: {fallback_details['node_question']}")
    print(f"   用户输入: {user_input}")
    print(f"   期望条件: {expected_conditions}")

    # 返回一个特殊的路由信号
    return "FALLBACK_TRIGGERED"








# ================================
# 6. 简化的SOP对话管理器 (核心改进)
# ================================
class SimpleSOPConversation:
    """
    简化的SOP对话管理器 - V2架构的核心改进
    更清晰的接口和状态管理，高可扩展性
    """

    def __init__(self, sop_data_file: str = "data/new_2fa_sop.json", user_profile: Optional[Dict[str, Any]] = None, disable_userprofile_skip: bool = False):
        self.sop_data = load_sop_data(sop_data_file)
        self.graph = self._build_graph()
        self.current_state = None
        self.user_profile = user_profile  # 存储userProfile
        self.disable_userprofile_skip = disable_userprofile_skip  # 禁用UserProfile跳过功能

        print(f"SOP system initialized successfully")
        if user_profile:
            print(f"✅ UserProfile已加载，包含 {len(user_profile)} 个字段")
        if disable_userprofile_skip:
            print("⚠️ UserProfile跳过功能已禁用，将使用随机路径选择")

    def _build_graph(self) -> StateGraph:
        """构建简洁的状态图 - 统一架构，过滤start_node和intent_node"""
        workflow = StateGraph(SOPState)

        # 过滤掉start_node和intent_node
        valid_nodes = {}
        for node_id, node_config in self.sop_data['nodes'].items():
            original_type = node_config.get('original_type', '')
            if original_type not in ['start_node', 'intent_node']:
                valid_nodes[node_id] = node_config

        # 为有效节点添加特定的执行器
        for node_id in valid_nodes.keys():
            workflow.add_node(node_id, create_node_executor(node_id))

        # 添加统一的路由逻辑
        for node_id in valid_nodes.keys():
            workflow.add_conditional_edges(
                node_id,
                smart_router,
                {next_id: next_id for next_id in valid_nodes.keys()} | {END: END}
            )

        # 找到真正的入口点（start_node指向的第一个有效节点）
        start_node_id = self.sop_data.get('start_nodes', [None])[0]
        entry_point = None

        if start_node_id and start_node_id in self.sop_data['nodes']:
            # 查找start_node的下一个节点
            adjacency = self.sop_data.get('adjacency_graph', {})
            start_edges = adjacency.get(start_node_id, [])

            for edge in start_edges:
                # 处理两种格式：字符串或字典
                if isinstance(edge, str):
                    target_node = edge
                else:
                    target_node = edge.get('target')
                if target_node and target_node in valid_nodes:
                    entry_point = target_node
                    break

        # 如果没找到，使用第一个有效节点
        if not entry_point and valid_nodes:
            entry_point = next(iter(valid_nodes.keys()))

        if entry_point:
            workflow.set_entry_point(entry_point)

        return workflow.compile()  # type: ignore

    def start_conversation(self, run_id: Optional[str] = None) -> tuple[str, Dict]:
        """开始对话 - 简化的接口"""
        session_id = f"sop_{uuid.uuid4().hex[:8]}"

        # 找到真正的入口点（跳过start_node和intent_node）
        start_node_id = self.sop_data.get('start_nodes', [None])[0]
        entry_point = None

        if start_node_id and start_node_id in self.sop_data['nodes']:
            # 查找start_node的下一个有效节点
            adjacency = self.sop_data.get('adjacency_graph', {})
            start_edges = adjacency.get(start_node_id, [])

            for edge in start_edges:
                # 处理两种格式：字符串或字典
                if isinstance(edge, str):
                    target_node = edge
                else:
                    target_node = edge.get('target')
                if target_node and target_node in self.sop_data['nodes']:
                    target_config = self.sop_data['nodes'][target_node]
                    if target_config.get('original_type', '') not in ['start_node', 'intent_node']:
                        entry_point = target_node
                        break

        # 如果没找到，使用第一个有效节点
        if not entry_point:
            for node_id, node_config in self.sop_data['nodes'].items():
                if node_config.get('original_type', '') not in ['start_node', 'intent_node']:
                    entry_point = node_id
                    break

        # 初始化状态
        self.current_state = SOPState(
            messages=[],
            current_node=entry_point,
            sop_data=self.sop_data,
            session_id=session_id,
            need_user_input=False,
            llm_decision=None,
            fallback_info=None,
            user_profile=self.user_profile,  # 添加userProfile到状态中
            condition_context=None,
            routing_context={}  # 初始化路由上下文
        )

        # 执行第一步 - 添加thread_id配置以支持LangSmith追踪
        config = {"configurable": {"thread_id": session_id}}
        result = self.graph.invoke(self.current_state, config=config)  # type: ignore
        if isinstance(result, dict):
            self.current_state.update(result)



        # 获取助手回复
        messages = self.current_state.get('messages', [])
        assistant_message = messages[-1]['content'] if messages else "Hello!"

        return assistant_message, {
            'session_id': session_id,
            'current_node': self.current_state['current_node'],
            'message_count': len(messages)
        }

    def process_user_input(self, user_input: str, run_id: Optional[str] = None) -> tuple[str, Dict]:
        """处理用户输入 - 修复终端节点处理"""
        if not self.current_state:
            return "Session not started", {"error": "No active session"}

        # 检查对话是否已结束
        if self.current_state.get('conversation_ended', False):
            print(f"对话已结束，不再处理新的输入")
            return "Thank you for your message. This conversation has ended. Please start a new conversation if you need further assistance.", {
                'current_node': self.current_state['current_node'],
                'message_count': len(self.current_state.get('messages', [])),
                'conversation_ended': True
            }

        # 添加用户消息
        self.current_state['messages'].append({"role": "user", "content": user_input})
        self.current_state['need_user_input'] = False

        if DEBUG_MODE:
            print(f"处理用户输入: '{user_input}'")

        # 直接进行路由决策，不重复执行当前节点
        current_node = self.current_state['current_node']

        # 检查当前节点是否为终端节点
        node_config = self.current_state['sop_data']['nodes'].get(current_node, {})
        next_nodes = node_config.get('next_nodes', [])
        is_terminal = not next_nodes

        # 如果是终端节点，直接结束对话
        if is_terminal:
            print(f"到达终端节点，对话结束")
            self.current_state['conversation_ended'] = True
            # 为终端节点生成最终响应
            final_message = _generate_end_message(current_node, self.current_state['sop_data'])
            self.current_state['messages'].append({"role": "assistant", "content": final_message})
        else:
            # 非终端节点，进行路由决策
            next_node_id = smart_router(self.current_state)

            if next_node_id == "FALLBACK_TRIGGERED":
                print("🚨 Fallback被触发，对话终止并返回详情。")
                self.current_state['conversation_ended'] = True
            elif next_node_id == "CONVERSATION_END":
                print(f"对话自然结束")
                self.current_state['conversation_ended'] = True
            elif next_node_id == "WAIT_USER_INPUT" or next_node_id == "STAY_CURRENT":
                print(f"保持在当前节点，等待更多用户输入")
                self.current_state['need_user_input'] = True
            elif next_node_id and next_node_id not in ["CONVERSATION_END", "WAIT_USER_INPUT", "STAY_CURRENT", "FALLBACK_TRIGGERED"]:
                if DEBUG_MODE:
                    print(f"路由跳转: {current_node[:12]} -> {next_node_id[:12]}")

                # 使用新的自动路由API，自动处理连续路由
                updated_state = execute_node_with_auto_routing(self.current_state, next_node_id)
                self.current_state.update(updated_state)
            else:
                print(f"未知路由状态，保持当前节点")
                self.current_state['need_user_input'] = True

        # 获取助手回复
        messages = self.current_state.get('messages', [])
        assistant_message = messages[-1]['content'] if messages and messages[-1]['role'] == 'assistant' else "Processing..."

        # 检查对话是否已结束或触发了Fallback
        conversation_ended = self.current_state.get('conversation_ended', False)
        fallback_info = self.current_state.get('fallback_info')

        # 准备最终的返回信息
        final_info = {
            'current_node': self.current_state['current_node'],
            'message_count': len(messages),
            'llm_decision': self.current_state.get('llm_decision'),
            'conversation_ended': conversation_ended,
            'status': 'fallback' if fallback_info else 'normal',
            'fallback_details': fallback_info
        }

        return assistant_message, final_info

    def get_session_info(self) -> Dict:
        """获取会话信息"""
        if not self.current_state:
            return {"error": "No active session"}

        return {
            'session_id': self.current_state['session_id'],
            'current_node': self.current_state['current_node'],
            'message_count': len(self.current_state.get('messages', [])),
            'llm_decision': self.current_state.get('llm_decision')
        }

# ================================
# 7. 工厂函数和实用工具
# ================================
def create_sop_conversation() -> SimpleSOPConversation:
    """创建SOP对话实例 - 简化的工厂函数"""
    return SimpleSOPConversation()




