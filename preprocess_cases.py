#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP测试用例数据预处理脚本
将原始CSV对话记录转换为结构化的测试剧本JSON格式

功能：
1. 读取data/cases/目录下的CSV文件
2. 合并连续的同类型消息（用户或客服）
3. 生成结构化的剧本JSON文件
4. 支持批量处理多个测试用例
"""

import csv
import json
import os
from typing import List, Dict, Any
from pathlib import Path

class CasePreprocessor:
    """测试用例预处理器"""
    
    def __init__(self, cases_dir: str = "data/cases"):
        self.cases_dir = Path(cases_dir)
        self.output_file = self.cases_dir / "processed_test_cases.json"
        
    def read_csv_file(self, csv_path: Path) -> List[Dict[str, Any]]:
        """读取CSV文件并返回消息列表"""
        messages = []
        
        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    # 解析CSV行
                    message = {
                        'session_id': row.get('session_id', ''),
                        'content': row.get('content', '').strip(),
                        'sender_type': int(row.get('sender_type', 0)),  # 1=用户, 2=客服
                        'msg_type': row.get('msg_type', ''),
                        'seq_no': int(row.get('seq_no', 0))
                    }
                    
                    # 过滤空消息
                    if message['content']:
                        messages.append(message)
                        
        except Exception as e:
            print(f"❌ 读取CSV文件失败 {csv_path}: {e}")
            return []
            
        # 按seq_no排序确保消息顺序正确
        messages.sort(key=lambda x: x['seq_no'])
        return messages
    
    def merge_consecutive_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并连续的同类型消息"""
        if not messages:
            return []
            
        merged_script = []
        current_turn = None
        turn_id = 0
        
        for msg in messages:
            sender_type = msg['sender_type']
            content = msg['content']
            
            # 确定说话者类型
            speaker = "user" if sender_type == 1 else "agent"
            
            # 如果是新的说话者或第一条消息，创建新的回合
            if current_turn is None or current_turn['speaker'] != speaker:
                # 保存上一个回合（如果存在）
                if current_turn is not None:
                    merged_script.append(current_turn)
                
                # 创建新回合
                current_turn = {
                    'turn_id': turn_id,
                    'speaker': speaker,
                    'text': content,
                    'original_messages': [msg]
                }
                turn_id += 1
            else:
                # 合并到当前回合，使用换行符连接
                current_turn['text'] += '\n' + content
                current_turn['original_messages'].append(msg)
        
        # 添加最后一个回合
        if current_turn is not None:
            merged_script.append(current_turn)
            
        return merged_script
    
    def process_single_case(self, csv_file: Path) -> Dict[str, Any]:
        """处理单个测试用例"""
        print(f"📝 处理测试用例: {csv_file.name}")
        
        # 提取case_id（文件名去掉.csv后缀）
        case_id = csv_file.stem
        
        # 读取CSV数据
        raw_messages = self.read_csv_file(csv_file)
        if not raw_messages:
            print(f"⚠️ 跳过空文件: {csv_file.name}")
            return None
            
        print(f"   原始消息数: {len(raw_messages)}")
        
        # 合并连续消息
        merged_script = self.merge_consecutive_messages(raw_messages)
        print(f"   合并后回合数: {len(merged_script)}")
        
        # 统计用户和客服回合
        user_turns = sum(1 for turn in merged_script if turn['speaker'] == 'user')
        agent_turns = sum(1 for turn in merged_script if turn['speaker'] == 'agent')
        print(f"   用户回合: {user_turns}, 客服回合: {agent_turns}")
        
        return {
            'case_id': case_id,
            'source_file': str(csv_file),
            'statistics': {
                'total_turns': len(merged_script),
                'user_turns': user_turns,
                'agent_turns': agent_turns,
                'original_messages': len(raw_messages)
            },
            'full_script': merged_script
        }
    
    def process_all_cases(self) -> List[Dict[str, Any]]:
        """处理所有测试用例"""
        print(f"🔍 扫描目录: {self.cases_dir}")
        
        # 查找所有CSV文件
        csv_files = list(self.cases_dir.glob("*.csv"))
        if not csv_files:
            print(f"❌ 未找到CSV文件在目录: {self.cases_dir}")
            return []
            
        print(f"📁 找到 {len(csv_files)} 个CSV文件")
        
        processed_cases = []
        
        for csv_file in sorted(csv_files):
            case_data = self.process_single_case(csv_file)
            if case_data:
                processed_cases.append(case_data)
                
        return processed_cases
    
    def save_processed_cases(self, processed_cases: List[Dict[str, Any]]) -> None:
        """保存处理后的测试用例"""
        if not processed_cases:
            print("❌ 没有有效的测试用例可保存")
            return
            
        # 创建输出数据
        output_data = {
            'metadata': {
                'total_cases': len(processed_cases),
                'processed_at': __import__('datetime').datetime.now().isoformat(),
                'source_directory': str(self.cases_dir)
            },
            'cases': processed_cases
        }
        
        # 保存到JSON文件
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 处理完成，保存到: {self.output_file}")
            print(f"📊 总计处理 {len(processed_cases)} 个测试用例")
            
            # 打印统计信息
            total_turns = sum(case['statistics']['total_turns'] for case in processed_cases)
            total_messages = sum(case['statistics']['original_messages'] for case in processed_cases)
            print(f"📈 统计: {total_messages} 条原始消息 → {total_turns} 个对话回合")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def run(self) -> None:
        """运行完整的预处理流程"""
        print("🚀 开始SOP测试用例预处理")
        print("=" * 50)
        
        # 检查输入目录
        if not self.cases_dir.exists():
            print(f"❌ 目录不存在: {self.cases_dir}")
            return
            
        # 处理所有用例
        processed_cases = self.process_all_cases()
        
        # 保存结果
        self.save_processed_cases(processed_cases)
        
        print("=" * 50)
        print("✅ 预处理完成")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SOP测试用例预处理工具')
    parser.add_argument('--cases-dir', default='data/cases', 
                       help='测试用例CSV文件目录 (默认: data/cases)')
    parser.add_argument('--single-case', 
                       help='只处理指定的单个CSV文件 (例如: 01.csv)')
    
    args = parser.parse_args()
    
    preprocessor = CasePreprocessor(args.cases_dir)
    
    if args.single_case:
        # 处理单个文件
        csv_path = Path(args.cases_dir) / args.single_case
        if csv_path.exists():
            case_data = preprocessor.process_single_case(csv_path)
            if case_data:
                preprocessor.save_processed_cases([case_data])
        else:
            print(f"❌ 文件不存在: {csv_path}")
    else:
        # 处理所有文件
        preprocessor.run()

if __name__ == "__main__":
    main()
