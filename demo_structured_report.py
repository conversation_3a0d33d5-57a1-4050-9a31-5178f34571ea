#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化报告演示脚本
展示真实SOP执行过程中的结构化路由和决策信息
"""

import os
import sys
from pathlib import Path

def run_structured_demo():
    """运行结构化报告演示"""
    
    print("🚀 结构化路由和决策报告演示")
    print("=" * 60)
    print("目标：展示SOP执行过程中的内部决策逻辑")
    print("特点：UserProfile跳过、条件判断、路由决策")
    print("=" * 60)
    
    # 步骤1: 启用调试模式以收集更多信息
    print("\n📝 步骤1: 启用详细追踪模式")
    print("-" * 30)
    
    # 修改sop_core的调试模式
    import sop_core
    original_debug = sop_core.DEBUG_MODE
    sop_core.DEBUG_MODE = True
    
    try:
        # 步骤2: 运行带UserProfile的测试
        print("正在运行带UserProfile的SOP测试...")
        result = os.system("python test_workflow_v0.1.py --single-case 01 --output-report structured_demo_report.md")
        
        if result == 0:
            print("✅ 测试执行完成")
        else:
            print("❌ 测试执行失败")
            return
        
        # 步骤3: 分析生成的报告
        print("\n📊 步骤2: 分析结构化报告")
        print("-" * 30)
        
        report_file = Path("structured_demo_report.md")
        if report_file.exists():
            print(f"📄 报告文件: {report_file}")
            
            # 读取并分析报告内容
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查报告中的关键部分
            sections = {
                "测试摘要": "## 📊 测试摘要" in content,
                "UserProfile信息": "#### 📋 UserProfile信息" in content,
                "节点执行路径": "#### 🛤️ 节点执行路径" in content,
                "内部决策记录": "#### 🔍 系统内部决策记录" in content,
                "路由决策分析": "#### ⚡ 条件判断和路由决策" in content,
                "用户交互记录": "## 💬 用户交互记录" in content
            }
            
            print("📋 报告内容分析:")
            for section, found in sections.items():
                status = "✅" if found else "❌"
                print(f"   {status} {section}")
            
            # 显示报告的关键部分
            print(f"\n📖 报告关键内容预览:")
            lines = content.split('\n')
            
            # 查找并显示UserProfile部分
            in_userprofile = False
            userprofile_lines = []
            for line in lines:
                if "#### 📋 UserProfile信息" in line:
                    in_userprofile = True
                elif in_userprofile and line.startswith("####"):
                    break
                elif in_userprofile:
                    userprofile_lines.append(line)
            
            if userprofile_lines:
                print("\n🔍 UserProfile信息:")
                for line in userprofile_lines[:10]:  # 显示前10行
                    if line.strip():
                        print(f"   {line}")
            
            # 查找并显示路径信息
            in_path = False
            path_lines = []
            for line in lines:
                if "#### 🛤️ 节点执行路径" in line:
                    in_path = True
                elif in_path and line.startswith("####"):
                    break
                elif in_path:
                    path_lines.append(line)
            
            if path_lines:
                print("\n🛤️ 节点执行路径:")
                for line in path_lines[:8]:  # 显示前8行
                    if line.strip():
                        print(f"   {line}")
            
            print(f"\n💡 完整报告请查看: {report_file}")
            
        else:
            print("❌ 未找到报告文件")
        
        # 步骤4: 展示报告的价值
        print(f"\n🎯 步骤3: 报告价值说明")
        print("-" * 30)
        print("📈 这份结构化报告为管理层提供:")
        print("   1. 量化的成功率和性能指标")
        print("   2. UserProfile的使用情况和效果")
        print("   3. SOP节点的执行路径和类型")
        print("   4. 系统内部的决策逻辑和推理过程")
        print("   5. 条件判断的已知/缺失信息分析")
        print("   6. 真实的用户-Agent交互记录")
        
        print(f"\n🔧 技术特点:")
        print("   • 数据驱动: 使用真实CSV对话数据")
        print("   • 智能匹配: LLM意图匹配和历史回填")
        print("   • 内部透明: 展示系统内部决策过程")
        print("   • 老板友好: 结构化、易读的Markdown格式")
        
    finally:
        # 恢复原始调试模式
        sop_core.DEBUG_MODE = original_debug
    
    print(f"\n✅ 结构化报告演示完成！")

def show_report_structure():
    """展示报告结构说明"""
    print("📋 结构化测试报告包含以下部分:")
    print("=" * 50)
    print()
    print("📊 1. 测试摘要")
    print("   - 总测试用例数、成功率")
    print("   - 平均执行步数和时间")
    print("   - 关键性能指标")
    print()
    print("🔍 2. 失败分析")
    print("   - 失败用例详情")
    print("   - 错误原因分析")
    print("   - 目标节点信息")
    print()
    print("📋 3. 详细测试结果表格")
    print("   - 每个用例的执行结果")
    print("   - 目标节点匹配情况")
    print("   - 执行性能指标")
    print()
    print("🧠 4. 结构化路由和决策分析")
    print("   📋 UserProfile信息")
    print("      - 可用字段数和字段列表")
    print("      - 样本数据展示")
    print("   🛤️ 节点执行路径")
    print("      - 每个步骤的节点类型和标题")
    print("      - 条件节点的选项和描述")
    print("   🔍 系统内部决策记录")
    print("      - 决策依据和推理过程")
    print("      - 跳过原因和信息来源")
    print("   ⚡ 条件判断和路由决策")
    print("      - 已知信息和缺失信息")
    print("      - LLM决策过程")
    print()
    print("💬 5. 用户交互记录")
    print("   - 真实的SOP-用户对话")
    print("   - 展示决策路径的具体过程")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--structure', '-s']:
        show_report_structure()
    elif len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print("结构化报告演示脚本")
        print("用法:")
        print("  python demo_structured_report.py           # 运行演示")
        print("  python demo_structured_report.py -s        # 显示报告结构")
        print("  python demo_structured_report.py --help    # 显示帮助")
    else:
        run_structured_demo()

if __name__ == "__main__":
    main()
