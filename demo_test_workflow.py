#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP测试工作流演示脚本
展示如何使用端到端测试工作流进行SOP性能验证
"""

import os
import sys
from pathlib import Path

def run_demo():
    """运行完整的测试工作流演示"""
    
    print("🚀 SOP Executor 端到端测试工作流演示")
    print("=" * 60)
    
    # 步骤1: 数据预处理
    print("\n📝 步骤1: 数据预处理")
    print("-" * 30)
    
    print("正在处理测试用例...")
    os.system("python preprocess_cases.py --single-case 01.csv")
    
    # 检查预处理结果
    processed_file = Path("data/cases/processed_test_cases.json")
    if processed_file.exists():
        print("✅ 数据预处理完成")
    else:
        print("❌ 数据预处理失败")
        return
    
    # 步骤2: 运行测试
    print("\n🧪 步骤2: 运行SOP测试")
    print("-" * 30)
    
    print("正在执行SOP路径模拟...")
    result = os.system("python test_workflow_v0.1.py --single-case 01")
    
    if result == 0:
        print("✅ 测试执行完成")
    else:
        print("❌ 测试执行失败")
        return
    
    # 步骤3: 查看报告
    print("\n📊 步骤3: 测试报告")
    print("-" * 30)
    
    # 查找最新的报告文件
    report_files = list(Path(".").glob("test_report_*.md"))
    if report_files:
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
        print(f"📄 最新测试报告: {latest_report}")
        
        # 显示报告摘要
        try:
            with open(latest_report, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 查找测试摘要部分
            in_summary = False
            for line in lines:
                if "## 📊 测试摘要" in line:
                    in_summary = True
                    continue
                elif in_summary and line.startswith("##"):
                    break
                elif in_summary and line.strip():
                    print(line.rstrip())
                    
        except Exception as e:
            print(f"⚠️ 无法读取报告: {e}")
    else:
        print("❌ 未找到测试报告")
    
    print("\n🎉 演示完成！")
    print("=" * 60)
    
    # 提供后续操作建议
    print("\n💡 后续操作建议:")
    print("1. 查看完整测试报告了解详细结果")
    print("2. 根据失败分析优化SOP逻辑")
    print("3. 添加更多测试用例进行全面验证")
    print("4. 集成到CI/CD流程中进行自动化测试")

def show_usage():
    """显示使用说明"""
    print("SOP测试工作流使用说明")
    print("=" * 40)
    print()
    print("🔧 基本用法:")
    print("  python demo_test_workflow.py        # 运行完整演示")
    print("  python demo_test_workflow.py --help # 显示此帮助")
    print()
    print("📁 手动执行步骤:")
    print("  1. 数据预处理:")
    print("     python preprocess_cases.py --single-case 01.csv")
    print()
    print("  2. 运行测试:")
    print("     python test_workflow_v0.1.py --single-case 01")
    print()
    print("  3. 查看报告:")
    print("     cat test_report_*.md")
    print()
    print("🎯 高级用法:")
    print("  # 处理所有测试用例")
    print("  python preprocess_cases.py")
    print("  python test_workflow_v0.1.py")
    print()
    print("  # 自定义报告文件名")
    print("  python test_workflow_v0.1.py --output-report my_report.md")
    print()
    print("  # 使用不同的SOP数据文件")
    print("  python test_workflow_v0.1.py --sop-data data/my_sop.json")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_usage()
    else:
        run_demo()

if __name__ == "__main__":
    main()
