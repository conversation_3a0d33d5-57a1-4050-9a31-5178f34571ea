"session_id","content","sender_type","msg_type","seq_no"
"*********","How to verify personal account","1","1","1"
"*********","Forgot My Account Details","1","1","8"
"*********","Forgot My Account Details","1","1","11"
"*********","I would like to verified my account","1","1","13"
"*********","Perform Self-Service: KYC Rejection","1","1","15"
"*********","How to verify personal account","1","1","18"
"*********","Not relevant","1","1","20"
"*********","Yes, transfer to Customer Service","1","1","23"
"*********","Welcome to Binance Customer Support! This is **🪻[<PERSON> <PERSON>]🪻** and it's my pleasure to assist you today.","2","1","37"
"*********","Can you please help to verify my identity I have been uploading so many and I get rejected for passing the verification","1","1","38"
"*********","Sure, let me help you.","2","1","39"
"*********","I don’t know what’s the problem","1","1","40"
"*********","Can you give me a photo of your ID?","2","1","41"
"*********","Thank you.","2","1","47"
"*********","Do you recognize this phone number 406***5326?","2","1","48"
"*********","Yes but I lost it I can’t find it my old number","1","1","49"
"*********","Understand.","2","1","50"
"*********","I would like to change my number to recent one","1","1","51"
"*********","Since you can only have one verified account, I will guide you to the old one.","2","1","52"
"*********","ok","1","1","53"
"*********","Do you remember password of your old account?","2","1","54"
"*********","yes","1","1","55"
"*********","Please don't send me yours.","2","1","56"
"*********","It's not safe to provide me or anyone password.","2","1","57"
"*********","Ok","1","1","58"
"*********","i can Logn in with my email address but I can’t login with my old phone number","1","1","59"
"*********","You actually can.","2","1","60"
"*********","But you will be stopped at verification step.","2","1","61"
"*********","Yeah ok can you please help to verify this account","1","1","62"
"*********","Now I need you to use your number and password to log in, when it asks for code, send me a screenshot.","2","1","63"
"*********","Ok","1","1","64"
"*********","Unfortunately, I won't be able to help you verify this account as you already have one, it would be better to get your old account back.","2","1","65"
"*********","should I use my old phone number","1","1","66"
"*********","Yes, that phone number.","2","1","67"
"*********","ok got it give one second","1","1","68"
"*********","I'll wait here for you.","2","1","69"
"*********","Can you please help me what’s my old number","1","1","70"
"*********","I even forgot it","1","1","71"
"*********","Ah I see.","2","1","72"
"*********","Yeah","1","1","73"
"*********","There's an appeal for this.","2","1","74"
"*********","yeah I can see but that’s not completely full number","1","1","75"
"*********","Does it show you option of ""Forgot Account""?","2","1","76"
"*********","I would give you your number if I can see the whole thing.","2","1","77"
"*********","Wait I think I remember now","1","1","78"
"*********","Great, can you type it out for me too?","2","1","79"
"*********","**********","1","1","80"
"*********","Let me check.","2","1","81"
"*********","is it correct one","1","1","82"
"*********","Let's try shall we?","2","1","83"
"*********","Remove the 0 in the front and use it to log in.","2","1","84"
"*********","yeah now I am trying to login in","1","1","85"
"*********","Take your time.","2","1","86"
"*********","ok","1","1","87"
"*********","😊","2","1","88"
"*********","now it says wrong password","1","1","89"
"*********","I actually use the correct one","1","1","90"
"*********","I see.","2","1","91"
"*********","when I login with my email","1","1","92"
"*********","it works","1","1","93"
"*********","Probably you didn't bind any email.","2","1","94"
"*********","But it didn’t work with phones","1","1","95"
"*********","That's my speculation.","2","1","96"
"*********","yeah","1","1","97"
"*********","There's an option to reset password.","2","1","98"
"*********","Can you try?","2","1","99"
"*********","ok","1","1","100"
"*********","Hope it works.","2","1","101"
"*********","can click continue","1","1","102"
"*********","Yes.","2","1","103"
"*********","now they asked me verification code","1","1","104"
"*********","but I can’t","1","1","105"
"*********","Do you see any **Security verification unavailable**?","2","1","106"
"*********","no","1","1","107"
"*********","i cant","1","1","108"
"*********","Thank you.","2","1","109"
"*********","Btw, can I have a screenshot too?","2","1","110"
"*********","yes I will send you now","1","1","111"
"*********","Thanks.","2","1","112"
"*********","i actually login in into my laptop","1","1","113"
"*********","just give me seconds","1","1","114"
"*********","I'll wait.","2","1","115"
"*********","I will send you","1","1","116"
"*********","No worries.","2","1","117"
"*********","Thank you.","2","1","123"
"*********","It shows like this","1","1","124"
"*********","I will now assist you to reset it.","2","1","125"
"*********","ok thanks","1","1","126"
"*********","But in order to do that, I will need you to open a new chat as visitor.","2","1","127"
"*********","where","1","1","128"
"*********","A visitor chat is a chat that allows users to talk to us without log in to any account.","2","1","129"
"*********","in here","1","1","130"
"*********","First, you would need to log out.","2","1","131"
"*********","Save this case ID *********.","2","1","132"
"*********","Close this chat, log out then join a new chat as visitor, I'll show you how.","2","1","133"
"*********","can I log out from laptop right","1","1","134"
"*********","### How to Create a Case as a Visitor on Binance:

1. **Visit Binance**: Go to [Binance.com](https://www.binance.com).
2. **Contact Support**: Click the button at the bottom right to contact Customer Support.
3. **Continue as Visitor**: Select ""Continue as Visitor"" in the chat window.
4. **Enter Contact Info**: Provide your phone number or email address.
5. **Select Question**: Choose the question related to your issue.
6. **Mark Unsolved**: Click ""Unsolved"" if the issue persists.
7. **Indicate Irrelevance**: Select ""Not relevant"" if solutions don't help.
8. **Transfer to CS**: Click ""Yes, transfer to Customer Service"" to escalate.

This concise guide helps you create a support case on Binance as a visitor.","2","1","135"
"*********","Ok","1","1","138"
"*********","You will be assigned someone new.","2","1","139"
"*********","Or me, but just give them this case ID *********.","2","1","140"
