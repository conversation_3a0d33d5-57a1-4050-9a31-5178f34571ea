{"01": {"case_file": "data/cases/01.csv", "ground_truth_node_title": "01. Request User to chat in again as Visitor or login to the account User want to perform 2FA reset", "description": "引导用户以访客身份重新聊天或登录他们想要重置2FA的账户。"}, "02": {"case_file": "data/cases/02.csv", "ground_truth_node_title": "02. Associate User in chat", "description": "在聊天中关联用户身份。"}, "03": {"case_file": "data/cases/03.csv", "ground_truth_node_title": "03. User request is under review", "description": "告知用户其请求正在审核中。"}, "04": {"case_file": "data/cases/04.csv", "ground_truth_node_title": "04. User's self-reset is approved and can login with the new 2FAs", "description": "用户的自助重置已批准，可以使用新的2FA登录。"}, "05": {"case_file": "data/cases/05.csv", "ground_truth_node_title": "05. Provide Face Attack disclaimer", "description": "提供关于人脸攻击的免责声明。"}, "06": {"case_file": "data/cases/06.csv", "ground_truth_node_title": "06. Transfer to CS Tier B - Security Issues", "description": "因安全问题转移至二线客服。"}, "07": {"case_file": "data/cases/07.csv", "ground_truth_node_title": "07. User try self-reset again", "description": "引导用户再次尝试自助重置。"}, "08": {"case_file": "data/cases/08.csv", "ground_truth_node_title": "08. User resolve the block first [ SOLVED ]", "description": "用户需要先解决账户封禁问题。"}, "09": {"case_file": "data/cases/09.csv", "ground_truth_node_title": "09. Reject User reset 2fa request", "description": "拒绝用户的2FA重置请求。"}, "10": {"case_file": "data/cases/10.csv", "ground_truth_node_title": "10. Guide user proceed to appeal", "description": "引导用户进行申诉。"}, "11": {"case_file": "data/cases/11.csv", "ground_truth_node_title": "11. User proceed to login and self reset password", "description": "引导用户登录并自助重置密码。"}, "12": {"case_file": "data/cases/12.csv", "ground_truth_node_title": "12. guide user to self-reset 2FA", "description": "引导用户自助重置2FA。"}, "13": {"case_file": "data/cases/13.csv", "ground_truth_node_title": "13. Advise User to login with new 2FA", "description": "建议用户使用新的2FA登录。"}, "14": {"case_file": "data/cases/14.csv", "ground_truth_node_title": "14. User case is being reviewed", "description": "告知用户的案例正在审核中。"}, "15": {"case_file": "data/cases/15.csv", "ground_truth_node_title": "15. User to resubmit files according to the email sent to User", "description": "要求用户根据收到的邮件重新提交文件。"}, "16": {"case_file": "data/cases/16.csv", "ground_truth_node_title": "16. Appeal passed, User may proceed to login with existing or new 2FA", "description": "申诉通过，用户可以使用现有或新的2FA登录。"}, "17": {"case_file": "data/cases/17.csv", "ground_truth_node_title": "17. Appeal rejected, advise User according to the “Reject Reason”", "description": "申诉被拒，根据拒绝原因向用户提供建议。"}, "18": {"case_file": "data/cases/18.csv", "ground_truth_node_title": "18. Transfer to Compliance (Reg LawEnforcement)", "description": "转移至合规部门（法律执行相关）。"}, "19": {"case_file": "data/cases/19.csv", "ground_truth_node_title": "19. Check with operator who placed the block for further instructions", "description": "联系实施封禁的操作员以获取进一步指示。"}, "20": {"case_file": "data/cases/20.csv", "ground_truth_node_title": "20. Follow offboarded instructions", "description": "遵循离职人员交接的指示。"}, "21": {"case_file": "data/cases/21.csv", "ground_truth_node_title": "21. <PERSON><PERSON> Support Ticket to the Risk team", "description": "向风控团队提交支持工单。"}, "22": {"case_file": "data/cases/22.csv", "ground_truth_node_title": "22. Check with respective BU who placed the block and follow their instructions", "description": "联系实施封禁的业务部门并遵循其指示。"}, "23": {"case_file": "data/cases/23.csv", "ground_truth_node_title": "23. Collect Statement Video", "description": "收集陈述视频。"}, "24": {"case_file": "data/cases/24.csv", "ground_truth_node_title": "24. Collect Statement Video + Deposit Video", "description": "收集陈述视频和存款视频。"}, "25": {"case_file": "data/cases/25.csv", "ground_truth_node_title": "25. Video Call with the User for Statement Video", "description": "与用户进行视频通话以录制陈述视频。"}, "26": {"case_file": "data/cases/26.csv", "ground_truth_node_title": "26. Collect Multiple Face Statement Video", "description": "收集多面部陈述视频。"}, "27": {"case_file": "data/cases/27.csv", "ground_truth_node_title": "27. Collect alternative materials together with Statement Video or Multiple Face Video", "description": "收集替代材料以及陈述视频或多面部视频。"}, "28": {"case_file": "data/cases/28.csv", "ground_truth_node_title": "28. Ask 6 security questions and collect Statement Video or Multiple Face Video", "description": "询问6个安全问题并收集陈述视频或多面部视频。"}, "29": {"case_file": "data/cases/29.csv", "ground_truth_node_title": "29. Reject User's reset 2FA request", "description": "拒绝用户的2FA重置请求。"}, "30": {"case_file": "data/cases/30.csv", "ground_truth_node_title": "30. Collect Statement Video and other relevant materials User can provide, then submit the reset request", "description": "收集用户能提供的陈述视频和其他相关材料，然后提交重置请求。"}}