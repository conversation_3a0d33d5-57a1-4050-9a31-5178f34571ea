#!/usr/bin/env python3
"""
从SOP数据生成基础路径文件
基于sop_langgraph_data.json生成sop_paths_extracted_nodes_only.json和sop_paths_extracted_full.json
"""

import json
from typing import Dict, List, Any
from collections import defaultdict

def load_sop_data(filename: str) -> Dict:
    """加载SOP数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✓ 成功加载 {filename}")
        return data
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return {}

def find_all_paths(sop_data: Dict) -> List[Dict]:
    """从SOP数据中找到所有可能的路径"""
    nodes = sop_data.get('nodes', {})
    adjacency_graph = sop_data.get('adjacency_graph', {})
    start_nodes = sop_data.get('start_nodes', [])
    
    print(f"📊 开始生成路径...")
    print(f"   节点数: {len(nodes)}")
    print(f"   起始节点: {start_nodes}")
    
    all_paths = []
    path_counter = 0
    
    for start_node in start_nodes:
        print(f"🔍 从起始节点 {start_node} 开始探索路径...")
        paths_from_start = explore_paths_from_node(start_node, nodes, adjacency_graph, path_counter)
        all_paths.extend(paths_from_start)
        path_counter += len(paths_from_start)
    
    print(f"✅ 总共生成 {len(all_paths)} 条路径")
    return all_paths

def explore_paths_from_node(start_node: str, nodes: Dict, adjacency_graph: Dict, start_counter: int) -> List[Dict]:
    """从指定节点开始探索所有可能的路径"""
    paths = []
    visited = set()
    
    def dfs(current_node: str, current_path: List[Dict], path_signature: str, condition_sequence: List[Dict]):
        if current_node in visited:
            return
        
        # 获取当前节点信息
        node_info = nodes.get(current_node, {})
        node_type = node_info.get('type', 'unknown')
        node_title = node_info.get('title', '')
        
        # 添加到路径中
        step_info = {
            'step': len(current_path) + 1,
            'node_id': current_node,
            'type': node_type,
            'title': node_title,
            'action': f"Process {node_type}: {node_title}"
        }
        
        current_path.append(step_info)
        
        # 更新路径签名
        if path_signature:
            path_signature += "-->"
        path_signature += f"{node_type}:{node_title}"
        
        # 检查是否是终端节点（没有出边）
        next_nodes = adjacency_graph.get(current_node, [])
        
        if not next_nodes:
            # 这是一个终端路径
            path_id = f"path_{start_counter + len(paths):04d}"
            
            path_data = {
                'path_id': path_id,
                'path_signature_nodes_only': path_signature,
                'description': path_signature,
                'steps': current_path.copy(),
                'condition_sequence': condition_sequence.copy(),
                'total_steps': len(current_path)
            }
            
            paths.append(path_data)
            return
        
        # 继续探索下一个节点
        for next_edge in next_nodes:
            next_node = next_edge['target']
            condition = next_edge.get('condition')
            
            # 如果是条件节点，记录条件选择
            if condition:
                condition_info = {
                    'step': len(current_path),
                    'node_id': current_node,
                    'question': node_title,
                    'choice': condition
                }
                condition_sequence.append(condition_info)
            
            dfs(next_node, current_path, path_signature, condition_sequence)
            
            # 回溯时移除条件选择
            if condition:
                condition_sequence.pop()
        
        # 回溯时移除当前步骤
        current_path.pop()
    
    # 开始深度优先搜索
    dfs(start_node, [], "", [])
    
    return paths

def save_paths_data(paths: List[Dict], output_file: str):
    """保存路径数据"""
    output_data = {
        'paths': paths,
        'total_paths': len(paths),
        'generated_from': 'sop_langgraph_data.json'
    }
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        print(f"✅ 路径数据已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")

def main():
    """主函数"""
    print("🚀 开始从SOP数据生成路径...")
    print("="*60)
    
    # 1. 加载SOP数据
    sop_data = load_sop_data('sop_langgraph_data.json')
    if not sop_data:
        print("❌ 无法加载SOP数据")
        return
    
    # 2. 生成所有路径
    all_paths = find_all_paths(sop_data)
    
    if not all_paths:
        print("❌ 没有生成任何路径")
        return
    
    # 3. 保存节点路径数据
    save_paths_data(all_paths, 'sop_paths_extracted_nodes_only.json')
    
    # 4. 保存完整路径数据（包含条件序列）
    save_paths_data(all_paths, 'sop_paths_extracted_full.json')
    
    # 5. 显示统计信息
    print(f"\n📊 路径生成统计:")
    print(f"   总路径数: {len(all_paths)}")
    
    # 显示前几条路径的示例
    print(f"\n🔍 前3条路径示例:")
    for i, path in enumerate(all_paths[:3]):
        print(f"\n路径 {i+1}: {path['path_id']}")
        print(f"  描述: {path['description'][:100]}...")
        print(f"  步骤数: {path['total_steps']}")
        print(f"  条件数: {len(path['condition_sequence'])}")
    
    print("\n✅ 路径生成完成!")
    print("="*60)

if __name__ == "__main__":
    main()














